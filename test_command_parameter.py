"""
Test script to verify the submit_to_shift command accepts the new parameter.
"""
import sys
import subprocess

def test_submit_to_shift_help():
    """Test that the new parameter appears in the help output."""
    print("Testing submit_to_shift command help...")
    
    try:
        # Test that the command can be imported and the help shows our new parameter
        result = subprocess.run([
            sys.executable, "-c", 
            "import sys; sys.path.insert(0, r'c:\\Users\\<USER>\\vscode\\pycharm\\elipy-scripts'); "
            "from dice_elipy_scripts.submit_to_shift import cli; "
            "print('✅ submit_to_shift command imported successfully'); "
            "help(cli)"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Command imported successfully")
            if "filter-latest-combined-builds" in result.stdout:
                print("✅ New parameter found in command")
                return True
            else:
                print("❌ New parameter not found in command")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return False
        else:
            print("❌ Command import failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = test_submit_to_shift_help()
    print("\n" + "="*50)
    if success:
        print("🎉 Command line parameter test passed!")
    else:
        print("❌ Command line parameter test failed!")
    sys.exit(0 if success else 1)
