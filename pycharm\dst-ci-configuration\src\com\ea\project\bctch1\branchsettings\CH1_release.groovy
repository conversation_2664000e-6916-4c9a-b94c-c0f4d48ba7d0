package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_release {
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_criterion + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        azure_fileshare         : [
            secret_context    : 'glacier_azure_fileshare',
            target_build_share: 'bfglacier',
        ],
        job_label_statebuild    : 'statebuild_criterion',
        autotest_remote_settings: [
            eala     : [
                credentials   : 'monkey.bct',
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001'
            ],
            criterion: [
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001'
            ],
            dice     : [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
        p4_code_creds           : 'perforce-battlefield-criterion',
        p4_data_creds           : 'perforce-battlefield-criterion',
        p4_code_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        p4_data_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]
    static Map code_settings = [
        code_reference_job           : 'CH1-release.copy-integrate-to.CH1-SP-release.start',
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
        statebuild_code              : false,
        trigger_type_code            : 'none',
    ]
    static Map data_settings = [
        slack_channel_patchdata      : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_patchdata         : false,
        statebuild_webexport         : false,
        webexport_branch             : true,
    ]
    static Map frosty_settings = [
        enable_eac_win64_digital      : true,
        enable_eac_win64_steam        : true,
        enable_eac_win64_combine      : true,
        enable_eac_win64_steam_combine: true,
        statebuild_frosty             : false,
        slack_channel_patchfrosty     : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_frosty          : 5,
        use_linuxclient               : true,
        enable_steam_drm_wrapping     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'ShippingLevels',
        server_asset              : 'Game/Setup/Build/MPLevels',
        baseline_set              : false,
        combine_bundles           : [
            combine_asset        : 'CombinedShippingMPLevels',
            combine_reference_job: 'CH1-SP-release.patchdata.start',
            is_target_branch     : true,
            source_branch_code   : 'CH1-SP-release',
            source_branch_data   : 'CH1-SP-release',
        ],
        drone_outsourcers         : ['Jukebox'],
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        move_location_parallel    : true,
        new_locations             : [
            DiceStockholm: [
                elipy_call_new_location: project.elipy_call + ' --use-fbenv-core',
            ],
            Montreal     : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect : [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        oreans_protection         : true,
        shift_branch              : true,
        shift_every_build         : false,
        skip_icepick_settings_file: true,
        slack_channel_shift       : [
            channels                  : ['#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        strip_symbols             : false,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'ps5', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'retail', 'performance']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.patchdata.start', args: []],
        [name: '.webexport.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'ps5'],
        [name: 'win64'],
        [name: 'xbsx'],
    ]
    static List patchdata_downstream_matrix = [
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.lastknowngood.start', args: []],
        [name: '.integration.start', args: []],
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs performance --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: ' --additional-configs final'],
                                 [format: 'combine', config: 'final', region: 'ww', args: ''],
                                 [format: 'combine', config: 'retail', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance --additional-configs release'],
                                   [format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'combine', config: 'final', region: 'ww', args: ''],
                                   [format: 'combine', config: 'retail', region: 'ww', args: ''],
                                   [format: 'steam_combine', config: 'final', region: 'ww', args: ''],
                                   [format: 'steam_combine', config: 'retail', region: 'ww', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: ' --additional-configs final'],
                                  [format: 'combine', config: 'final', region: 'ww', args: ''],
                                  [format: 'combine', config: 'retail', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.shift.start', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.combine.ww.final', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
        [name: '.win64.upload_to_steam.combine.ww.retail', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.shift.upload', downstream_job: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']],
    ]
}
