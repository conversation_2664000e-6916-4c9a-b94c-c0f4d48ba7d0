# Shift Upload Debug Mode Implementation Summary

## Overview
Successfully implemented a debug mode for shift uploads that allows users to see what builds would be uploaded without actually performing the uploads. This is useful for debugging and verification purposes.

## Implementation Details

### 1. CLI Parameter Addition
**File:** `dice_elipy_scripts/submit_to_shift.py`

Added a new command-line option:
```python
@click.option(
    "--debug-print-only",
    is_flag=True,
    default=False,
    help="If true, only print the paths that would be uploaded without actually uploading builds. "
    "Used for debugging purposes.",
)
```

### 2. Parameter Passing
The `debug_print_only` parameter is:
- Added to the CLI function signature 
- Included in the kwargs dictionary that's passed to the shifter factory
- Forwarded to all shifter implementations via the ShiftUtils base class

### 3. ShiftUtils Base Class Updates
**File:** `elipy2/shift_utils.py`

**Constructor Changes:**
- Added `debug_print_only=False` parameter with default value False
- Added `self.debug_print_only = debug_print_only` instance variable assignment

**Upload Logic Changes:**
- Modified `_upload_single_build()` method's `upload_build()` nested function
- Added debug mode check before performing actual upload operations:
  ```python
  # Debug mode: only print paths without uploading
  if self.debug_print_only:
      LOGGER.info("DEBUG MODE: Would upload build at path: {}".format(shift_dir))
      LOGGER.info("DEBUG MODE: Build name: {}".format(shift_data["buildname"]))
      LOGGER.info("DEBUG MODE: SKU ID: {}".format(shift_data["skuid"]))
      LOGGER.info("DEBUG MODE: Platform: {}".format(shift_data["platform"]))
      if fil is not None:
          LOGGER.info("DEBUG MODE: Files to upload: [{}]".format(fil))
      else:
          LOGGER.info("DEBUG MODE: Files to upload: {}".format(shift_data["filename"]))
      return
  ```

### 4. Shifter Class Updates
**File:** `elipy2/shifters.py`

**FrostyShifter Changes:**
- Added debug mode check in `process_shift_upload()` after build discovery
- Prints all found build paths and exits early when debug mode is enabled:
  ```python
  # Debug mode: print all build paths without uploading
  if self.debug_print_only:
      LOGGER.info("DEBUG MODE: Only printing build paths, no actual uploads will occur")
      for i, shift_dir in enumerate(shift_dirs, 1):
          LOGGER.info("DEBUG MODE: Build {}/{}: {}".format(i, len(shift_dirs), shift_dir))
      return
  ```

**OffsiteDroneShifter Changes:**
- Added debug mode check in `process_shift_upload()` after determining build location
- Prints the build path and exits early when debug mode is enabled:
  ```python
  # Debug mode: print build path without uploading
  if self.debug_print_only:
      LOGGER.info("DEBUG MODE: Only printing build path, no actual upload will occur")
      LOGGER.info("DEBUG MODE: Build path: {}".format(shift_dir))
      return
  ```

## Usage Examples

### Enable Debug Mode
```bash
python -m dice_elipy_scripts.submit_to_shift \
    --user "username" \
    --password "password" \
    --code-branch "main" \
    --code-changelist "12345" \
    --debug-print-only
```

### Disable Debug Mode (Default Behavior)
```bash
python -m dice_elipy_scripts.submit_to_shift \
    --user "username" \
    --password "password" \
    --code-branch "main" \
    --code-changelist "12345"
```

## Debug Output Examples

### FrostyShifter Debug Output
```
DEBUG MODE: Only printing build paths, no actual uploads will occur
DEBUG MODE: Build 1/3: /path/to/build1
DEBUG MODE: Build 2/3: /path/to/build2  
DEBUG MODE: Build 3/3: /path/to/build3
```

### Individual Build Debug Output
```
DEBUG MODE: Would upload build at path: /path/to/specific/build
DEBUG MODE: Build name: MyGame Release 12345
DEBUG MODE: SKU ID: abcd1234-5678-90ef-ghij-klmnopqrstuv
DEBUG MODE: Platform: ps5
DEBUG MODE: Files to upload: ['game.pkg', 'update.pkg']
```

### OffsiteDroneShifter Debug Output
```
DEBUG MODE: Only printing build path, no actual upload will occur
DEBUG MODE: Build path: /path/to/drone/build
```

## Benefits

1. **No Network Activity**: When debug mode is enabled, no files are copied, no network connections are made, and no uploads occur
2. **Quick Validation**: Allows verification of build discovery logic and path resolution without time-consuming uploads
3. **Debugging Aid**: Helps troubleshoot issues with build detection, filtering, and configuration
4. **Safe Testing**: Can be used in production environments without risk of uploading test builds
5. **Comprehensive Information**: Shows detailed information about what would be uploaded including build names, SKUs, platforms, and files

## Backward Compatibility

- Default behavior remains unchanged (debug mode is False by default)
- Existing scripts and automation continue to work without modifications
- No breaking changes to API or CLI interface

## Testing

All modified files pass Python syntax validation:
- ✓ `submit_to_shift.py` - Valid syntax
- ✓ `shift_utils.py` - Valid syntax  
- ✓ `shifters.py` - Valid syntax

The implementation is ready for use and testing in development environments.
