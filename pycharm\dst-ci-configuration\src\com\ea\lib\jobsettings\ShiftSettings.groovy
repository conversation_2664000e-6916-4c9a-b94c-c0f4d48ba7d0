package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class ShiftSettings extends JobSetting {
    static final String SHIFTER_TYPE_OFFSITE_BASIC_DRONE = 'offsite_basic_drone_shifter'
    static final String SHIFTER_TYPE_FROSTY_SHIFTER = 'frosty_shifter'
    static final String SHIFTER_TYPE_OFFSITE_DRONE = 'offsite_drone_shifter'
    Map fbLoginDetails
    String referenceJob
    String triggerType

    void initializeShiftStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, ['shift'], 'disable_build', false)
        referenceJob = branchInfo.shift_reference_job ?: "${branchInfo.branch_name}.frosty.start"
        description = "Scheduler to shift start job for ${branchName}"
        cronTrigger = branchInfo.trigger_string_shift ?: 'TZ=Europe/Stockholm \n H 0,13 * * 1-6\nH 6,13 * * 7'
        triggerType = branchInfo.trigger_type_shift ?: 'cron'
        concurrentBuilds = branchInfo.concurrent_builds_shift ?: 3
    }

    void initializeShiftUpload(def branchFile, def masterFile, def projectFile, String branchName, String shifterType = null, Boolean codeOnly = false) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        buildName = codeOnly ? '${JOB_NAME}.${ENV, var="code_changelist"}' : '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        description = 'Job for uploading builds to Shift.'
        elipyInstallCall = branchInfo.elipy_install_call
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile)
        def shiftCompression = LibCommonNonCps.get_setting_value(branchInfo, [], 'shift_compression', false, projectFile)
        concurrentBuilds = branchInfo.concurrent_builds_shift ?: 3
        timeoutMinutes = branchInfo.shift_upload_timeout_hours ? branchInfo.shift_upload_timeout_hours * 60 : 480

        // Extra args
        extraArgs = branchInfo.extra_shift_args ?: ''
        extraArgs += codeOnly ? '' : " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist%"
        extraArgs += ' --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN%'
        if ([null, true].contains(branchInfo.elipy_shift_config)) {
            extraArgs += ' --use-elipy-config'
        }
        extraArgs += branchInfo.elipy_shift_submission_tool ? " --submission-tool ${branchInfo.elipy_shift_submission_tool}" : ''
        extraArgs += shiftCompression ? ' --compression true' : ''
        extraArgs += branchInfo.shift_retention_policy ? " --shift-retention-policy ${branchInfo.shift_retention_policy}" : ''
        extraArgs += shifterType ? " --shifter-type ${shifterType}" : ' --shifter-type frosty_shifter'
        extraArgs += branchInfo.force_reshift ? ' --force-reshift true' : ' --force-reshift %force_reshift%'
        if (shifterType == SHIFTER_TYPE_OFFSITE_DRONE) {
            extraArgs += branchInfo.use_zipped_drone_builds ? ' --use-zipped-drone-builds' : ''
        }
        if (shifterType == null || shifterType == SHIFTER_TYPE_FROSTY_SHIFTER) {
            extraArgs += ' --build-id %build_id%'
        }

        // Job labels
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        if (shifterType == SHIFTER_TYPE_OFFSITE_BASIC_DRONE) {
            jobLabel = "${branchInfo.job_label_statebuild ?: 'statebuild'} || ${branchInfo.data_branch} && data && server"
        } else {
            def notStatebuildFrosty = branchInfo.statebuild_patchfrosty == false || branchInfo.statebuild_frosty == false
            if (branchInfo.statebuild_data == false && branchInfo.job_label_statebuild == null && notStatebuildFrosty) {
                // makes it possible to set job_label_statebuild to 'data_branch util' and have it run on a util machine instead
                jobLabel = "${branchInfo.data_branch} && data && server"
                // This uses a machine that has synced data, and builds the job with the shortest build time.
            }
        }

        elipyCmd = this.elipyCall + ' submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd%' + ' --code-branch ' + branchInfo.code_branch + ' --code-changelist %code_changelist%' + extraArgs
    }

    void initializeProcessShiftSubscriptionDownloads(def branchFile, def masterFile, def projectFile, String branchName, Map shiftSubscriptionInfo) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        buildName = '${JOB_NAME}.${BUILD_NUMBER}'
        cronTrigger = shiftSubscriptionInfo.trigger_string ?: 'H/10 * * * 1-6\nH 6-23/2 * * 7'
        description = 'Job for processing builds downloaded with a Shift subscription, and registering these in Bilbo.'
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile)
        jobLabel = shiftSubscriptionInfo.job_label
        triggerType = shiftSubscriptionInfo.trigger_type ?: 'cron'

        Integer timeoutHours = shiftSubscriptionInfo.timeout_hours ?: 4
        timeoutMinutes = timeoutHours * 60

        String buildType = shiftSubscriptionInfo.build_type ?: 'code'
        String newElipyCall = this.elipyCall.replace("--location ${shiftSubscriptionInfo.src_location}", "--location ${shiftSubscriptionInfo.dest_location}")
        elipyCmd = newElipyCall + ' process_shift_subscription_downloads --build-type ' + buildType + ' --code-branch ' + branchInfo.code_branch
    }
}
