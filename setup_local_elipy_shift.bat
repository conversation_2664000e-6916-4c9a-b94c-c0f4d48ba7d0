@echo off
setlocal enabledelayedexpansion

echo ================================================
echo Setting up local elipy environment for shift upload debug
echo ================================================

REM Activate virtual environment
echo Activating elipy virtual environment...
call .\elipy_venv\Scripts\activate
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    echo Please ensure elipy_venv exists and is properly set up
    pause
    exit /b 1
)

REM Set environment variables for local testing
echo Setting up environment variables...
set ELIPY_CONFIG=%~dp0pycharm\elipy2\elipy2\elipy_example.yml
set TNT_ROOT=%~dp0tnt_root
set GAME_DATA_DIR=%~dp0game_data_dir
set GAME_ROOT=%~dp0game_root
set ELIPY_TEST_RUN=TRUE

echo ELIPY_CONFIG=%ELIPY_CONFIG%
echo TNT_ROOT=%TNT_ROOT%
echo GAME_DATA_DIR=%GAME_DATA_DIR%
echo GAME_ROOT=%GAME_ROOT%

REM Create required directories if they don't exist
echo Creating required directories...
if not exist "%TNT_ROOT%" mkdir "%TNT_ROOT%"
if not exist "%GAME_DATA_DIR%" mkdir "%GAME_DATA_DIR%"
if not exist "%GAME_ROOT%" mkdir "%GAME_ROOT%"
if not exist "test_builds" mkdir test_builds

REM Check if elipy packages are installed
echo Checking elipy installation...
python -c "import elipy2; print('elipy2 found at:', elipy2.__file__)" 2>nul
if errorlevel 1 (
    echo Installing elipy2 in development mode...
    cd pycharm\elipy2
    pip install -e .
    if errorlevel 1 (
        echo ERROR: Failed to install elipy2
        pause
        exit /b 1
    )
    cd ..\..
)

python -c "import dice_elipy_scripts; print('dice_elipy_scripts found at:', dice_elipy_scripts.__file__)" 2>nul
if errorlevel 1 (
    echo Installing dice_elipy_scripts in development mode...
    cd pycharm\elipy-scripts
    pip install -e .
    if errorlevel 1 (
        echo ERROR: Failed to install dice_elipy_scripts
        pause
        exit /b 1
    )
    cd ..\..
)

REM Test the elipy command
echo ================================================
echo Testing elipy command...
echo ================================================
python -m elipy2.cli --help | head -10

echo ================================================
echo Environment setup complete!
echo ================================================
echo.
echo You can now run your command:
echo.
echo elipy --location DiceStockholm --use-fbenv-core submit_to_shift --user <EMAIL> --password **** --code-branch CH1-content-dev --code-changelist 24664622 --data-branch CH1-content-dev --data-changelist 24664622 --artifactory-user **** --artifactory-apikey **** --use-elipy-config --compression true --shifter-type frosty_shifter --force-reshift true --build-id None --debug-print-only
echo.
echo Or the equivalent direct Python command:
echo.
echo python -m dice_elipy_scripts.submit_to_shift --user <EMAIL> --password **** --code-branch CH1-content-dev --code-changelist 24664622 --data-branch CH1-content-dev --data-changelist 24664622 --artifactory-user **** --artifactory-apikey **** --use-elipy-config --compression true --shifter-type frosty_shifter --force-reshift true --build-id None --debug-print-only
echo.
echo ================================================
