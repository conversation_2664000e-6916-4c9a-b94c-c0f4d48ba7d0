package com.ea.lib

import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.matrixfiles.AutotestMatrix
import hudson.model.Result

class BuildSelector {

    private final Object steps
    // RunWrapper
    private final Object currentBuild
    // EnvActionImpl
    private final Object env
    private final Object params

    /**
     * Class for running the build selector
     * @param steps The WorkflowScript context
     * @param currentBuild the start job's currentBuild
     * @param env the start job's environment
     */
    BuildSelector(steps, currentBuild, env, params) {
        this.steps = steps
        this.currentBuild = currentBuild
        this.env = env
        this.params = params
    }

    /**
     * Composes a key for the result map
     * @param platform Platform to compose the key from
     * @return The platform's name concatenated with the region, if defined
     */
    static String composeResultKey(Platform platform) {
        // CPS method mismatch
        // codenarc-disable UnnecessaryToString
        return "${platform.toString()}${platform.region ? "-${platform.region.toString()}" : ''}"
    }

    /**
     * Composes a key for the result map
     * @param platform Platform to compose the key from
     * @param region Region to compose the key from
     * @return The platform's name concatenated with the region, if defined
     */
    static String composeResultKey(String platform, String region) {
        // CPS method mismatch
        // codenarc-disable UnnecessaryToString
        return "${platform}${region ? "-${region.toString()}" : ''}"
    }

    /**
     * Runs the build-selector
     * @param autotestMatrix which AutotestMatrix configuration to use
     * @param branchName the branch it runs on
     * @param jobUrl The URL to the start job
     */
    Result trigger(AutotestMatrix autotestMatrix, String branchName, String jobUrl) {
        List<JobReference> jobReferences = []
        Map<String, Map> resultMap = [:]
        List<Platform> platforms = []
        def finalResult = steps.retryOnFailureCause(3, jobReferences, true) {
            platforms = []
            currentBuild.displayName = env.JOB_NAME
            String dataChangelist = params.data_changelist
            String codeChangelist = params.code_changelist
            String clientBuildId = params.client_build_id
            String serverBuildId = params.server_build_id
            AutotestCategory testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)

            if (testCategory.isTestWithLooseFiles) {
                List<Platform> defaultPlatforms = autotestMatrix.getPlatforms(branchName)
                platforms.addAll(LibAutotestModelBuilder.composeBuildSelectorPlatforms(testCategory.testInfo, defaultPlatforms))
            } else {
                platforms << new Platform(name: Name.ANY)
            }

            if (dataChangelist && codeChangelist && clientBuildId) {
                platforms.each { platform ->
                    resultMap[composeResultKey(platform)] = [
                        dataChangelist: dataChangelist,
                        codeChangelist: codeChangelist,
                        clientBuildId : clientBuildId,
                        serverBuildId : serverBuildId,
                    ]
                }
            } else {
                String jobName = env.branchName + '.build.selector'
                Map jobs = [:]
                platforms.each { platform ->
                    // CPS method mismatch
                    // codenarc-disable UnnecessaryToString
                    jobs["${jobName}.${platform.toString()}"] = composeJob(testCategory, jobName, resultMap, platform, jobUrl, jobReferences)
                }
                steps.parallel(jobs)
            }
        }
        if (noChangelistsFound(resultMap)) {
            steps.catchError(buildResult: Result.NOT_BUILT.toString(), stageResult: Result.UNSTABLE.toString()) {
                steps.error 'No changelist to test could be found. Aborting pipeline.'
            }
            currentBuild.displayName = "${env.JOB_NAME}.no-changelists-to-run"
            return Result.NOT_BUILT
        } else if (resultMap.size() != platforms.size()) {
            steps.echo "Couldn't find a CL for all platforms. Continuing, but as an UNSTABLE build."
            currentBuild.result = Result.UNSTABLE.toString()
            finalResult = finalResult.combine(Result.UNSTABLE)
        }

        String postFix = ''
        resultMap.each { platform, changelists ->
            String postChangelist = changelists.dataChangelist && changelists.codeChangelist ?
                "${changelists.dataChangelist}.${changelists.codeChangelist}" : 'no-cl'
            postFix += ".${platform}.${postChangelist}"
        }
        currentBuild.displayName = "${env.JOB_NAME}${postFix}"

        steps.echo 'Build selector results:'
        resultMap.each { platform, target ->
            if (target['dataChangelist'] && target['codeChangelist'] && target['clientBuildId']) {
                steps.echo "-- Running tests for platform ${platform} --\n" +
                    "on data CL: ${target['dataChangelist']}\n" +
                    "on code CL: ${target['codeChangelist']}\n" +
                    "with build id: ${target['clientBuildId']}"
            } else {
                steps.echo "-- Changelists for platform ${platform} not found. Skipping --\n" +
                    "data CL: ${target['dataChangelist']}\n" +
                    "code CL: ${target['codeChangelist']}\n" +
                    "with build id: ${target['clientBuildId']}"
                currentBuild.result = Result.UNSTABLE.toString()
                finalResult = finalResult.combine(Result.UNSTABLE)
            }
        }

        // Remove null values
        resultMap = resultMap.findAll {
            it.value.codeChangelist && it.value.dataChangelist && it.value.clientBuildId
        }

        def injectMap = [
            target_build_info: resultMap.inspect(),
        ]
        steps.EnvInject(currentBuild, injectMap)
        return finalResult
    }

    /**
     * Composes a build trigger closure for the build-selector.
     * @param testCategory The test category to determine changelists for
     * @param jobName Name of the job to run
     * @param resultMap Map to store the result on.
     * @param platform Platform to determine the changelists for, set it to LibBuildSelector.ANY_PLATFORM if it doesn't matter
     * @param startJobUrl The URL to the start job
     * @param jobReferences jobReferences to retry on failure
     * @return Closure that triggers the build
     */
    Closure composeJob(AutotestCategory testCategory, String jobName, Map resultMap, Platform platform, String startJobUrl,
                       List<JobReference> jobReferences) {
        List<String> requiredPlatforms = testCategory.testInfo.requiredPlatforms ?: testCategory.requiredPlatforms
        List<String> clientPlatforms = testCategory.testInfo.clientPlatforms ?: testCategory.clientPlatforms
        boolean needGameServer = testCategory.needGameServer
        String requiredPlatformsArgs = ''
        String clientPlatformsArgs = ''
        String serverPlatform = testCategory.testInfo.serverPlatform ?: testCategory.serverPlatform
        if (needGameServer && !serverPlatform) {
            serverPlatform = 'server'
        }

        if (serverPlatform) {
            requiredPlatforms.add(serverPlatform)
        }
        requiredPlatforms.each { requiredPlatform ->
            requiredPlatformsArgs += " --required-platforms ${requiredPlatform}"
        }
        clientPlatforms.each { clientPlatform ->
            clientPlatformsArgs += " --client-platforms ${clientPlatform}"
        }

        Boolean useShiftBuild = testCategory.testInfo.useShiftBuild
        useShiftBuild = useShiftBuild != null ? useShiftBuild : testCategory.useShiftBuild

        Boolean useLatestDrone = testCategory.testInfo.useLatestDrone
        useLatestDrone = useLatestDrone != null ? useLatestDrone : testCategory.useLatestDrone

        Boolean useSpinBuild = testCategory.testInfo.useSpinBuild
        useSpinBuild = useSpinBuild != null ? useSpinBuild : testCategory.useSpinBuild

        Boolean useAzureDroneBuild = testCategory.isFrostedAutotest

        def parameters = [
            steps.booleanParam(name: 'use_shift_build', value: useShiftBuild),
            steps.booleanParam(name: 'use_latest_drone', value: useLatestDrone),
            steps.booleanParam(name: 'use_spin_build', value: useSpinBuild),
            steps.booleanParam(name: 'use_azure_drone_build', value: useAzureDroneBuild),
            steps.string(name: 'client_platforms', value: clientPlatformsArgs),
        ]

        if (testCategory.isTestWithLooseFiles) {
            int timeoutHours = AutotestMatrix.getTimeoutHours(testCategory)
            String config = testCategory.testInfo.config ?: testCategory.config ?: 'final'
            String region = platform.region ? platform.region.toString() : Region.WW.toString()
            parameters.addAll([
                steps.string(name: 'platform', value: platform.toString()),
                steps.string(name: 'region', value: region),
                steps.string(name: 'config', value: config),
                steps.string(name: 'is_test_with_loose_files', value: '--is-test-with-loose-files'),
                steps.string(name: 'required_platforms', value: requiredPlatformsArgs),
                steps.string(name: 'build_timeout_hours', value: timeoutHours.toString()),
                steps.string(name: 'start_job_url', value: startJobUrl),
            ])
        }

        return {
            def buildSelector = steps.build(job: jobName, parameters: parameters, propagate: false)
            Closure callback = { downstreamJob ->
                String dataChangelist = downstreamJob.buildVariables?.data_changelist
                String codeChangelist = downstreamJob.buildVariables?.code_changelist
                String clientBuildId = downstreamJob.buildVariables?.client_build_id
                String serverBuildId = downstreamJob.buildVariables?.server_build_id

                steps.echo "data CL: ${dataChangelist} \n" +
                    "code CL: ${codeChangelist} \n" +
                    "clientBuildId: ${clientBuildId} \n" +
                    "serverBuildId: ${serverBuildId}"
                resultMap[composeResultKey(platform)] = [
                    dataChangelist: dataChangelist,
                    codeChangelist: codeChangelist,
                    clientBuildId : clientBuildId,
                    serverBuildId : serverBuildId,
                ]
            }
            jobReferences << new JobReference(downstreamJob: buildSelector, jobName: jobName, parameters: parameters, propagate: false,
                buildCallback: callback)
            callback(buildSelector)
            LibJenkins.printRunningJobs(steps)
        }
    }

    /**
     * Checks whether or not the results Map contains any changelists
     * @param results Map to analyze
     * @return true if no changelists were found, false otherwise
     */
    private static boolean noChangelistsFound(Map<String, Map> results) {
        if (!results) {
            return true
        }
        for (result in results) {
            if (result.value.dataChangelist || result.value.codeChangelist || result.value.clientBuildId || result.value.serverBuildId) {
                return false
            }
        }
        return true
    }

}
