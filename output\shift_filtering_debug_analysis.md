# Shift Build Filtering Debug Analysis

## Issue Identified
You're seeing 230 builds in logs instead of the expected 17 builds, suggesting the combined build filtering isn't working properly.

## Root Cause Found
The original filtering logic had two main issues:

### 1. Incorrect Combined Build Type Detection
**Original logic:** Only looked for `digital_combined` and `patch_combine`
**Your builds have:** `digital_combine`, `steam_combine` (without the 'd')

### 2. Incorrect Path Parsing
**Original logic:** Expected changelists at fixed positions (3 and 5 after combine type)
**Your actual structure:** 
```
win64\digital_combine\ww\final\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497
platform/combine_type/region/config/data-branch/data-changelist/code-branch/code-changelist
```

## Fixes Applied

### 1. Enhanced Combined Build Detection
```python
# Now detects all combine types
combined_types = ["digital_combine", "steam_combine", "patch_combine", "digital_combined"]
```

### 2. Flexible Changelist Parsing
```python
# Now searches for any numeric values as changelists
for i in range(combined_idx + 1, len(path_parts)):
    try:
        changelist_num = int(path_parts[i])
        # Use first numeric as data changelist, second as code changelist
    except ValueError:
        continue
```

### 3. Enhanced Grouping Logic
```python
# Group by platform + config + combine_type for more precise filtering
group_key = "{}_{}_{}".format(platform, config, found_combined_type)
```

### 4. Added Comprehensive Logging
- Logs before/after filtering counts
- Shows which builds are kept/filtered
- Indicates if filtering is enabled/disabled

## Test Results
Using your 17 example builds:
- **Before filtering:** 17 builds
- **After filtering:** 15 builds (removed 2 duplicates)
- **Filtered out:** 
  - `win64\steam_combine\ww\retail\...` (duplicate of final version)
  - `win64\digital_combine\ww\retail\...` (duplicate of final version)

## Next Steps

### To Debug Your 230 Build Issue:

1. **Run with debug mode** to see filtering in action:
   ```bash
   python -m dice_elipy_scripts.submit_to_shift \
       --your-normal-parameters \
       --debug-print-only
   ```

2. **Check the logs for:**
   - "Combined build filtering enabled: True/False"
   - "Before filtering: Found X builds"
   - "After filtering: Found Y builds"

3. **Possible causes for 230 builds:**
   - Filtering is disabled (`--no-filter-latest-combined-builds` flag used)
   - Build discovery finding many more builds than expected
   - Different combined build patterns not covered by the logic

### Quick Fix Options:

1. **Ensure filtering is enabled:** Don't use `--no-filter-latest-combined-builds`
2. **Test the fix:** Use `--debug-print-only` to verify filtering works
3. **Report results:** Share the debug output to see what's happening

## Expected Outcome
With the fixes, you should see:
- Significant reduction in build count (from 230 to a much smaller number)
- Only the latest combined builds per platform/config/type
- Faster upload times due to fewer redundant uploads
