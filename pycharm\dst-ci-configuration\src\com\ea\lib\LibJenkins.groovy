package com.ea.lib

import com.ea.CobraLogger
import hudson.EnvVars
import hudson.PluginManager
import hudson.console.ModelHyperlinkNote
import hudson.model.Cause
import hudson.model.Computer
import hudson.model.Descriptor
import hudson.model.Fingerprint
import hudson.model.Item
import hudson.model.Job
import hudson.model.Node
import hudson.model.Queue
import hudson.model.Result
import hudson.model.Run
import hudson.model.TaskListener
import hudson.model.TopLevelItem
import hudson.util.RunList
import jenkins.model.Jenkins

class LibJenkins {

    /**
     * Returns the latest code changelist of the given job that was a SUCCESS
     * @param jobName the job to look for
     * @return Code changelist
     */
    static String getLastStableCodeChangelist(String jobName) {
        return getEnvironmentForLastStableBuild(jobName)?.code_changelist ?: ''
    }

    /**
     * Returns the latest data changelist of the given job that was a SUCCESS
     * @param jobName the job to look for
     * @return Data changelist
     */
    static String getLastStableDataChangelist(String jobName) {
        return getEnvironmentForLastStableBuild(jobName)?.data_changelist ?: ''
    }

    /**
     * Returns the latest combined code changelist of the given job that was a SUCCESS
     * Falls back to regular code changelist if combine changelist is not available
     * @param jobName the job to look for
     * @return Combined code changelist
     * @throws IllegalStateException if neither combine nor regular code changelist can be retrieved
     */
    static String getLastStableCombineCodeChangelist(String jobName) {
        def env = getEnvironmentForLastStableBuild(jobName)
        def combineChangelist = env?.combine_code_changelist
        if (combineChangelist) {
            return combineChangelist
        }

        def regularChangelist = env?.code_changelist
        if (regularChangelist) {
            return regularChangelist
        }

        throw new IllegalStateException("Unable to retrieve stable code changelist from ${jobName}")
    }

    /**
     * Returns the latest combined data changelist of the given job that was a SUCCESS
     * Falls back to regular data changelist if combine changelist is not available
     * @param jobName the job to look for
     * @return Combined data changelist
     * @throws IllegalStateException if neither combine nor regular data changelist can be retrieved
     */
    static String getLastStableCombineDataChangelist(String jobName) {
        def env = getEnvironmentForLastStableBuild(jobName)
        def combineChangelist = env?.combine_data_changelist
        if (combineChangelist) {
            return combineChangelist
        }

        def regularChangelist = env?.data_changelist
        if (regularChangelist) {
            return regularChangelist
        }

        throw new IllegalStateException("Unable to retrieve stable data changelist from ${jobName}")
    }

    /**
     * Returns the environment variables for the latest build of the given jobName
     * @param jobName The job to look for
     * @return The environment variables as a Map
     */
    static EnvVars getEnvironmentForLastStableBuild(String jobName) {
        return getItem(jobName)?.lastStableBuild?.getEnvironment(TaskListener.NULL)
    }

    /**
     * Determines if the last {@code count} consecutive jobs have failed
     * @param jobName The job to query
     * @param buildNumber the build number of the job
     * @param count how many jobs to analyze
     * @return true if they failed, false otherwise
     */
    static boolean hasLastNumberOfJobsFailed(String jobName, String buildNumber, int count) {
        int lowerRange = buildNumber.toInteger() - count + 1
        if (lowerRange <= 0) {
            return false
        }
        Fingerprint.RangeSet rs = Fingerprint.RangeSet.fromString("${lowerRange}-${buildNumber}", true)
        return !getItem(jobName).getBuilds(rs).any {
            return it.result != Result.FAILURE
        }
    }

    /**
     * Retrieves an item with the given name, for instance a job.
     * @param name item to retrieve
     * @return the item
     */
    static TopLevelItem getItem(String name) {
        return Jenkins.get().getItem(name)
    }

    /**
     * Gets all the {@link Item}s recursively in the {@link hudson.model.ItemGroup} tree
     * and filter them by the given type.
     * @param type The type of items to retrieve
     * @return The items
     */
    static <T extends Item> List<T> getAllItems(Class<T> type) {
        return Jenkins.get().getAllItems(type)
    }

    /**
     * Returns the Jenkins master's base URL
     * @return root URL
     */
    static String getRootUrl() {
        return Jenkins.get().rootUrl
    }

    /**
     * Returns all the jobs that have been created by the seed on this master, including orphaned ones, but excluding manually created ones.
     * @return All jobs that have been created by the seed
     */
    static Map getAllGeneratedJobs() {
        def jenkins = Jenkins.get()
        PluginManager.UberClassLoader loader = (PluginManager.UberClassLoader) jenkins.pluginManager.uberClassLoader
        return jenkins.getDescriptorByType(loader.loadClass('javaposse.jobdsl.plugin.DescriptorImpl') as Class<Descriptor>)
            .generatedJobMap
    }

    /**
     * Returns all jobs that should exist on this master. The ones created by the seed and all the manually created jobs
     * @return All jobs that should exist on this master
     */
    static List<TopLevelItem> getJobs() {
        return Jenkins.get().items
    }

    /**
     * Returns the build Log for the latest job with the given name
     * @param jobName the job to look for
     * @return the log as a String
     */
    static String getLastBuildLog(String jobName) {
        return getItem(jobName)?.lastBuild?.logFile?.text
    }

    /**
     * Checks whether or not the last build with the given name is a SUCCESS
     * @param jobName The job to check
     * @return True if a SUCCESS, false otherwise
     */
    static boolean isLastBuildSuccess(String jobName) {
        return getItem(jobName)?.lastBuild?.result == Result.SUCCESS
    }

    /**
     * Retrieves all nodes with the given labels
     * @param labels The labels to look for
     * @return A list of Nodes with the given labels
     */
    static List<Node> getNodesWithLabels(List<String> labels) {
        return Jenkins.get().nodes.findAll { Node node ->
            labels.every { String label ->
                node.labelString.contains(label)
            }
        }
    }

    /**
     * Returns the nodes with the given prefix
     * @param prefix name prefix
     * @return A list of nodes
     */
    static List<Node> getNodesWithPrefix(String prefix) {
        return Jenkins.get().nodes.findAll { Node node ->
            node.name.startsWith(prefix)
        }
    }

    /**
     * Returns the jobs in queue for the given job
     * @param jobName the job to query
     * @return a list of items
     */
    static List<Queue.Item> getJobsInQueue(String jobName) {
        return jobsInQueue.findAll {
            it.task.name == jobName
        }
    }

    /**
     * Returns all the items in queue on the controller
     * @return all the items in queue
     */
    static Queue.Item[] getJobsInQueue() {
        return Jenkins.get().queue.items
    }

    /**
     * Returns the max amount of cloud virtual machines that this controller is configured for
     * @return The max virtual machines limit on the controller
     */
    static int getMaxCloudVirtualMachinesLimit() {
        int maxCloudVmLimit = 0
        Jenkins.get().clouds.each {
            maxCloudVmLimit += it.maxVirtualMachinesLimit
        }
        return maxCloudVmLimit
    }

    /**
     * Checks if there are onPrem agents with the given labels available for job.
     * @param labels The labels to look for
     * @return true if there are any idle machines with the given labels, false otherwise
     */
    static boolean onPremAgentsWithLabelsAvailable(List<String> labels) {
        def nodes = Jenkins.get().nodes.findAll { Node node ->
            labels.every { String label ->
                node.labelString.contains(label)
            } && !node.labelString.contains('cloud')
        }
        return nodes.any { it.toComputer() != null && it.toComputer().partiallyIdle }
    }

    /**
     * Retrieves the Nodes to run preflight maintenance on. Looks for nodes that are not offline or busy, also makes sure that the nodes
     * haven't run preflight recently.
     * @param dataset label
     * @param branchName label
     * @param platform label
     * @param nodeIdleTimeMinutes Skips Nodes that have run a preflight in the given time frame in minutes
     * @return A List of Nodes to run maintenance on
     */
    static List<String> getNodesToRunPreflightMaintenanceOn(String dataset, String branchName, String platform, int nodeIdleTimeMinutes) {
        List<String> labels = [dataset, platform, branchName]
        List<Node> jenkinsNodes = getNodesWithLabels(labels)
        String regexPattern = "${branchName}.*(preflight|postpreflight).(${platform})*"
        long nodeIdleTimeMilliseconds = nodeIdleTimeMinutes * 60L * 1000L
        jenkinsNodes.removeAll { Node node ->
            Computer computer = node.toComputer()
            if (computer == null || computer.offline || computer.countBusy() > 0) {
                CobraLogger.info("Skipping Maintenance on: ${node.nodeName} because it is either offine or it is running jobs")
                return true
            }
            // Skip nodes that recently ran a preflight
            RunList builtJobsOnNode = computer.builds.byTimestamp(System.currentTimeMillis() - nodeIdleTimeMilliseconds,
                System.currentTimeMillis())
            def run = builtJobsOnNode.find {
                it =~ regexPattern
            }
            if (run) {
                CobraLogger.info("Skipping Maintenance on: ${node.nodeName}")
                return true
            }
            return false
        }
        return jenkinsNodes*.nodeName
    }

    /**
     * Removes a node from Jenkins
     * @param nodeName Name of the node
     */
    static void removeNode(String nodeName) {
        CobraLogger.info("Trying to remove node ${nodeName}")
        Jenkins jenkins = Jenkins.get()
        Node node = jenkins.getNode(nodeName)
        if (node) {
            jenkins.removeNode(node)
        } else {
            CobraLogger.warning("Could not find the node $nodeName. The node will not be removed.")
        }
    }

    /**
     * Looks at the latest successful build of the target job and returns the changelists if they are newer than {@code jobName}'s
     * changelists.
     * @param jobName the base job
     * @param targetJobName target job that we want to compare with
     * @return the changelists of the target job if any of them are higher, an empty Map otherwise
     */
    static Map retrieveIfTargetChangelistsAreHigher(String jobName, String targetJobName) {
        EnvVars targetJob = getEnvironmentForLastStableBuild(targetJobName)
        EnvVars job = getEnvironmentForLastStableBuild(jobName)
        if (targetJob && !job) {
            return [
                code_changelist: targetJob.code_changelist,
                data_changelist: targetJob.data_changelist,
            ]
        } else if (targetJob && job) {
            int targetCodeChangelist = Integer.parseInt(targetJob.code_changelist as String)
            int targetDataChangelist = Integer.parseInt(targetJob.data_changelist as String)
            int codeChangelist = Integer.parseInt(job.code_changelist as String)
            int dataChangelist = Integer.parseInt(job.data_changelist as String)
            if (targetCodeChangelist > codeChangelist || targetDataChangelist > dataChangelist) {
                return [
                    code_changelist: targetJob.code_changelist,
                    data_changelist: targetJob.data_changelist,
                ]
            }
        }
        return [:]
    }

    /**
     * Prints the currently running pipeline job's downstream jobs
     * @param env the pipeline job's environment
     */
    static void printRunningJobs(def context) {
        try {
            def mainBuild = getItem(context.env.JOB_NAME as String).getBuildByNumber(context.env.BUILD_NUMBER as Integer)
            StringJoiner sj = new StringJoiner('\n- ', 'The following downstream builds are still running:\n- ', '')
            boolean downstreamJobsStillRunning = false
            getAllItems(Job).each { Job<Job, Run> job ->
                job.builds.each { Run run ->
                    if (run.building) {
                        Cause.UpstreamCause cause = run.getCause(Cause.UpstreamCause) as Cause.UpstreamCause
                        if (cause && cause.pointsTo(mainBuild)) {
                            downstreamJobsStillRunning = true
                            sj.add(ModelHyperlinkNote.encodeTo("/${run.url}", run.fullDisplayName))
                        }
                    }
                }
            }
            if (downstreamJobsStillRunning) {
                context.echo sj.toString()
            }
        } catch (NoSuchElementException e) {
            context.echo e.message
        }
    }

    /**
     * If the downstream job failed and allowFailure is set to true, it prints a message letting the user know that the failure was allowed.
     * @param context the scheduler context
     * @param downstreamJob the downstream job to analyze
     * @param allowFailure whether to print the message on a failure
     */
    static void printFailureMessage(def context, def downstreamJob, boolean allowFailure) {
        if (downstreamJob.result == Result.FAILURE.toString() && allowFailure) {
            def jobEnv = downstreamJob.rawBuild.getEnvironment(TaskListener.NULL)
            def nodeText = jobEnv.NODE_NAME ?: '(missing node name)'
            String job = ModelHyperlinkNote.encodeTo(downstreamJob.rawBuild.url, downstreamJob.rawBuild.fullDisplayName)
            String node = ModelHyperlinkNote.encodeTo("/computer/${jobEnv.NODE_NAME}", nodeText)
            context.echo "The failed $job that completed on $node is allowed to fail (controlled by 'allow_failure: true' in the settings)."
        }
    }
}
