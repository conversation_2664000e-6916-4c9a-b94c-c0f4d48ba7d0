# Combined Build Shift Upload Optimization

## Problem Statement

Shift upload times for Single Player were over 10 hours due to multiple combined build CLs being uploaded (up to 7 of them), causing several TBs of data to be uploaded and missing QV testing deadlines.

## Solution

Implemented filtering logic to only upload the latest combined build for each platform/configuration, significantly reducing upload times while maintaining all necessary functionality.

## Changes Made

### 1. Core Filtering Logic (`elipy2/shift_utils.py`)

Added `_filter_latest_combined_builds()` method to the `ShiftUtils` class:

- **Purpose**: Filters combined builds to keep only the latest one per platform/configuration
- **Logic**: Groups builds by platform+config key, compares by (data_changelist, code_changelist) tuple
- **Path Structure**: Handles paths like `.../platform/digital_combined/patch_combine/config/combine-data-branch/combine-data-changelist/combine-code-branch/combine-code-changelist`
- **Preservation**: Keeps all regular builds unchanged, only filters combined builds

### 2. Integration (`elipy2/shifters.py`)

Modified `FrostyShifter.process_shift_upload()` method:

- **Integration Point**: Added filtering call after finding builds but before upload loop
- **Configurable**: Uses `self.filter_latest_combined_builds` parameter to control filtering
- **Logging**: Enhanced logging to show filtering results

### 3. Command Line Interface (`dice_elipy_scripts/submit_to_shift.py`)

Added new command line parameter:

- **Parameter**: `--filter-latest-combined-builds/--no-filter-latest-combined-builds`
- **Default**: `True` (filtering enabled by default)
- **Purpose**: Allows users to control filtering behavior for backward compatibility

### 4. Parameter Propagation

Updated constructor parameters throughout the call chain:

- `submit_to_shift.py` → `ShifterFactory` → `FrostyShifter` → `ShiftUtils`
- Added `filter_latest_combined_builds` parameter with default value `True`

## Technical Details

### Filtering Algorithm

1. **Classification**: Separate builds into combined builds vs regular builds
2. **Grouping**: Group combined builds by `platform_config` key
3. **Comparison**: For each group, select build with highest `(data_changelist, code_changelist)` priority
4. **Combination**: Merge filtered combined builds with all regular builds

### Path Structure Support

The implementation correctly handles the combined build path structure:
```
.../platform/digital_combined/patch_combine/config/combine-data-branch/combine-data-changelist/combine-code-branch/combine-code-changelist
```

Example:
```
\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8767
```
- Platform: `ps5`
- Config: `final`
- Data Changelist: `5680`
- Code Changelist: `8767`

### Error Handling

- Invalid paths are treated as regular builds
- Missing path components are handled gracefully
- Logging provides visibility into filtering decisions

## Test Results

Comprehensive testing showed:

- **Input**: 9 builds (2 regular + 7 combined)
- **Output**: 5 builds (2 regular + 3 latest combined)
- **Reduction**: 44% fewer builds uploaded (4 duplicate combined builds eliminated)

Test scenarios validated:
- ✅ Mixed combined and regular builds
- ✅ Filtering disabled mode
- ✅ No combined builds present
- ✅ Invalid path handling
- ✅ Changelist priority comparison

## Acceptance Criteria Validation

### ✅ Only the latest SP combined build is uploaded, not ALL of them

**Status: ACHIEVED**

- Implementation filters combined builds by platform/configuration
- Only the latest build (highest changelist numbers) is kept for each group
- All duplicate combined builds are eliminated
- Test results show 7 combined builds reduced to 3 (latest of each platform/config)

### ✅ Configurable behavior with backward compatibility

**Status: ACHIEVED**

- Added `--filter-latest-combined-builds` parameter (default: True)
- Users can disable filtering with `--no-filter-latest-combined-builds`
- Existing behavior preserved when filtering is disabled
- No breaking changes to existing workflows

## Performance Impact

### Expected Improvements

Based on the problem description and test results:

- **Before**: Up to 7 combined builds uploaded per platform/config
- **After**: Only 1 combined build uploaded per platform/config
- **Reduction**: ~85% fewer combined builds uploaded
- **Time Savings**: Significant reduction from 10+ hour uploads

### Storage Savings

- **Before**: Several TBs of duplicate combined builds
- **After**: Only latest builds uploaded, eliminating redundant data
- **Bandwidth**: Reduced network utilization during uploads

## Implementation Notes

### Design Decisions

1. **Default Enabled**: Filtering is enabled by default to immediately address the performance issue
2. **Configurable**: Filtering can be disabled for debugging or specific use cases
3. **Non-Breaking**: Regular builds are completely unaffected
4. **Platform Agnostic**: Works with any platform (PS5, WIN64, etc.) and configuration (final, release, etc.)

### Logging Enhancements

Added detailed logging for:
- Number of builds filtered
- Which builds were kept/removed
- Platform/config groupings
- Priority comparisons

Example log output:
```
Filtered combined builds: 9 -> 5 (removed 4 duplicate combined builds)
Keeping latest combined build for ps5_final: .../5680/...8767 (priority: (5680, 8767))
```

## Future Considerations

### Potential Enhancements

1. **Configuration File**: Move default filtering behavior to configuration file
2. **Time-Based Filtering**: Consider build timestamps in addition to changelist numbers
3. **Metrics Collection**: Add telemetry to track filtering effectiveness
4. **Branch-Specific Settings**: Allow different filtering behavior per branch

### Monitoring

Recommended monitoring:
- Upload time improvements
- Build count reductions
- Error rates (should remain unchanged)
- User feedback on new default behavior

## Files Modified

1. `elipy2/shift_utils.py` - Added filtering method and parameter
2. `elipy2/shifters.py` - Integrated filtering into upload process
3. `dice_elipy_scripts/submit_to_shift.py` - Added command line parameter

## Testing

Created comprehensive test suite:
- `test_filtering_logic.py` - Unit tests for filtering algorithm
- All tests pass with 100% success rate
- Syntax validation completed on all modified files

---

**Summary**: This implementation successfully addresses the shift upload performance issue by reducing duplicate combined builds while maintaining full backward compatibility and configurability.
