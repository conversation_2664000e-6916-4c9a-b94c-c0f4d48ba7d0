#!/bin/bash
set -euo pipefail

# =====================
# Constants
# =====================
DEFAULT_REPO="dre-cobra/packer-windows"        # Can be overridden by arguments or environment variables
DEFAULT_API_URL="https://gitlab.ea.com/api/v4"
ASSET_NAME="release_asset.json"                # Can be overridden by arguments or environment variables

# =====================
# Print usage information
# =====================
usage() {
  local EXIT_CODE=1
  if [ "$1" = "help" ]; then
    EXIT_CODE=0
  fi
  cat <<EOF >&2
Usage: $0 -d <datacenter> [-r <repo>] [-t <gitlab_token>] [-a <asset_name>] [-h]
  -d <datacenter>     (required) The datacenter to search for.
  -r <repository>     (optional) The GitLab repo in 'group/project' format. Default: $DEFAULT_REPO or envvar <PERSON>.
  -t <gitlab_token>   (optional) The GitLab token. Default: envvar GITLAB_TOKEN.
  -a <asset_name>     (optional) The asset name to look for. Default: $ASSET_NAME.
  -h                  Show this help message.
If -r, -t, or -a are not provided, the script will use the REPO, GITLAB_TOKEN, and ASSET_NAME environment variables, or their defaults.
EOF
  exit $EXIT_CODE
}


# =====================
# Print error and exit
# =====================
error_exit() {
  echo "[ERROR] $1" >&2
  exit "${2:-1}"
}


# =====================
# Parse command-line arguments
# =====================
parse_args() {
  REPO_ARG=""
  GITLAB_TOKEN_ARG=""
  ASSET_NAME_ARG=""
  while getopts "d:r:t:a:h" OPT; do
    case $OPT in
      d) DATACENTER="$OPTARG" ;;
      r) REPO_ARG="$OPTARG" ;;
      t) GITLAB_TOKEN_ARG="$OPTARG" ;;
      a) ASSET_NAME_ARG="$OPTARG" ;;
      h) usage help ;;
      *) usage ;;
    esac
  done
  if [ -z "$DATACENTER" ]; then
    usage
  fi
}


# =====================
# Set repo and token variables from args or environment
# =====================
set_env_vars() {
  REPO="${REPO_ARG:-${REPO:-$DEFAULT_REPO}}"
  if [ -n "$GITLAB_TOKEN_ARG" ]; then
    GITLAB_TOKEN="$GITLAB_TOKEN_ARG"
  fi
  if [ -n "$ASSET_NAME_ARG" ]; then
    ASSET_NAME="$ASSET_NAME_ARG"
  fi
  if [ -z "$GITLAB_TOKEN" ]; then
    error_exit "GITLAB_TOKEN must be provided via -t or environment variable." 5
  fi
}


# =====================
# Fetch releases JSON from GitLab API
# =====================
fetch_releases() {
  local REPO_ENCODED
  REPO_ENCODED=$(echo -n "$REPO" | sed 's/\//%2F/g')
  local URL="$DEFAULT_API_URL/projects/${REPO_ENCODED}/releases"
  RELEASES=$(curl --max-time 30 --retry 3 --retry-delay 1 --fail --header "PRIVATE-TOKEN: $GITLAB_TOKEN" --silent "$URL") \
    || error_exit "Failed to fetch releases from GitLab API using curl." 2
}


# =====================
# Find the template name for the given datacenter in the releases
# =====================
find_template_name() {
  local RELEASE_COUNT I TAG ASSETS_URL ASSET_HASH FINAL_URL RESPONSE TEMPLATE_NAME PROJECT_ID
  RELEASE_COUNT=$(echo "$RELEASES" | jq 'length')

  for ((I=0; I<RELEASE_COUNT; I++)); do
    TAG=$(echo "$RELEASES" | jq -r ".[${I}].tag_name")
    ASSETS_URL=$(echo "$RELEASES" | jq -r ".[${I}].assets.links[] | select(.name==\"$ASSET_NAME\") | .url")

    if [[ "$ASSETS_URL" =~ /uploads/([^/]+)/$ASSET_NAME$ ]]; then
      ASSET_HASH="${BASH_REMATCH[1]}"
    fi

    if [[ "$ASSETS_URL" =~ /project/([0-9]+)/uploads/([^/]+)/$ASSET_NAME$ ]]; then
      PROJECT_ID="${BASH_REMATCH[1]}"
      ASSET_HASH="${BASH_REMATCH[2]}"
    fi

    FINAL_URL="$DEFAULT_API_URL/projects/$PROJECT_ID/uploads/$ASSET_HASH/$ASSET_NAME"

    if [ -z "$FINAL_URL" ] || [ "$FINAL_URL" = "null" ]; then
      FINAL_URL="$ASSETS_URL"
    fi
    if [ -z "$FINAL_URL" ] || [ "$FINAL_URL" = "null" ]; then
      continue
    fi

    if ! RESPONSE=$(curl --max-time 30 --retry 3 --retry-delay 1 --fail --header "PRIVATE-TOKEN: $GITLAB_TOKEN" --location --silent --header "Accept: application/octet-stream" "$FINAL_URL"); then
      error_exit "Failed to download $ASSET_NAME from $FINAL_URL"
    fi

    if ! echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null; then
      error_exit "Downloaded file for tag $TAG is not a JSON array. Content:\n$RESPONSE"
    fi

    TEMPLATE_NAME=$(echo "$RESPONSE" | jq -r --arg DC "$DATACENTER" '.[] | select(.datacenter == $DC) | .template_name')
    if [ -n "$TEMPLATE_NAME" ] && [ "$TEMPLATE_NAME" != "null" ]; then
      echo "$RESPONSE" | jq -c --arg DC "$DATACENTER" '.[] | select(.datacenter == $DC)'
      return 0
    fi
  done

  error_exit "Datacenter '$DATACENTER' not found in any release's $ASSET_NAME file." 3
}


# =====================
# Main logic
# =====================
main() {
  parse_args "$@"
  set_env_vars
  fetch_releases

  local FOUND
  FOUND=$(find_template_name)
  echo "$FOUND" | jq
  exit 0
}


main "$@"
