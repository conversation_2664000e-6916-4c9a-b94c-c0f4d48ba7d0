package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)
/**
 * shift_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelists') {
            steps {
                script {
                    String lastCodeBuild = LibJenkins.getLastStableCodeChangelist(env.shift_reference_job as String)
                    String lastDataBuild = LibJenkins.getLastStableDataChangelist(env.shift_reference_job as String)

                    def codeChangelist = params.code_changelist ? params.code_changelist.trim() : lastCodeBuild
                    def dataChangelist = params.data_changelist ? params.data_changelist.trim() : lastDataBuild

                    if (codeChangelist == null || dataChangelist == null) {
                        error('Missing changelist, aborting build!')
                    }

                    def lastCodeBuilt = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String) ?: '1234'
                    def lastDataBuilt = LibJenkins.getLastStableDataChangelist(env.JOB_NAME as String) ?: '1234'

                    def injectMap = [
                        'code_changelist': codeChangelist,
                        'data_changelist': dataChangelist,
                        'last_code_built': lastCodeBuilt,
                        'last_data_built': lastDataBuilt,
                    ]

                    // Handle combined changelists for streams with combined builds
                    def hasCombinedBuilds = branchfile.standard_jobs_settings?.combine_bundles?.is_target_branch ?: false
                    if (hasCombinedBuilds) {
                        String lastCombineCodeBuild = LibJenkins.getLastStableCombineCodeChangelist(env.shift_reference_job as String)
                        String lastCombineDataBuild = LibJenkins.getLastStableCombineDataChangelist(env.shift_reference_job as String)
                        def combineCodeChangelist = params.combine_code_changelist ? params.combine_code_changelist.trim() : lastCombineCodeBuild
                        def combineDataChangelist = params.combine_data_changelist ? params.combine_data_changelist.trim() : lastCombineDataBuild

                        def lastCombineCodeBuilt = LibJenkins.getLastStableCombineCodeChangelist(env.JOB_NAME as String) ?: '1234'
                        def lastCombineDataBuilt = LibJenkins.getLastStableCombineDataChangelist(env.JOB_NAME as String) ?: '1234'

                        injectMap.putAll([
                            'combine_code_changelist': combineCodeChangelist,
                            'combine_data_changelist': combineDataChangelist,
                            'last_combine_code_built': lastCombineCodeBuilt,
                            'last_combine_data_built': lastCombineDataBuilt,
                        ])

                        currentBuild.displayName = env.JOB_NAME + '.' + dataChangelist + '.' + codeChangelist + '.combine.' + combineDataChangelist + '.' + combineCodeChangelist
                    } else {
                        currentBuild.displayName = env.JOB_NAME + '.' + dataChangelist + '.' + codeChangelist
                    }

                    EnvInject(currentBuild, injectMap)
                }
            }
        }
        stage('Trigger shift upload') {
            steps {
                script {
                    def args = [
                        string(name: 'code_changelist', value: env.code_changelist),
                        string(name: 'data_changelist', value: env.data_changelist),
                    ]

                    // Add combined changelist arguments for streams with combined builds
                    def hasCombinedBuilds = branchfile.standard_jobs_settings?.combine_bundles?.is_target_branch ?: false
                    if (hasCombinedBuilds) {
                        args += [
                            string(name: 'combine_code_changelist', value: env.combine_code_changelist),
                            string(name: 'combine_data_changelist', value: env.combine_data_changelist),
                        ]
                    }

                    // Check if we need to trigger based on regular changelists
                    boolean regularChangelistsMatch = (env.code_changelist == env.last_code_built && env.data_changelist == env.last_data_built)

                    // Check if we need to trigger based on combined changelists (only if they exist)
                    boolean combinedChangelistsMatch = true
                    if (hasCombinedBuilds) {
                        combinedChangelistsMatch = (env.combine_code_changelist == env.last_combine_code_built && env.combine_data_changelist == env.last_combine_data_built)
                    }

                    // Skip build only if both regular and combined changelists match (when applicable)
                    if (regularChangelistsMatch && combinedChangelistsMatch) {
                        if (hasCombinedBuilds) {
                            echo('Last build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                                ' with combine CL ' + env.last_combine_data_built + '.' + env.last_combine_code_built +
                                ' and current is CL ' + env.data_changelist + '.' + env.code_changelist +
                                ' with combine CL ' + env.combine_data_changelist + '.' + env.combine_code_changelist + ', aborting build.')
                        } else {
                            echo('Last code build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                                ' and current is CL ' + env.data_changelist + '.' + env.code_changelist + ', aborting build.')
                        }
                        currentBuild.result = Result.UNSTABLE.toString()
                    } else {
                        if (hasCombinedBuilds) {
                            echo('Last build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                                ' with combine CL ' + env.last_combine_data_built + '.' + env.last_combine_code_built +
                                ' but current is ' + env.data_changelist + '.' + env.code_changelist +
                                ' with combine CL ' + env.combine_data_changelist + '.' + env.combine_code_changelist + ', proceeding.')
                        } else {
                            echo('Last build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                                ' but current is ' + env.data_changelist + '.' + env.code_changelist + ', proceeding.')
                        }
                        List<JobReference> jobReferences = []
                        retryOnFailureCause(3, jobReferences) {
                            String jobName = env.branch_name + '.shift.upload'
                            def shiftJob = build(job: jobName, parameters: args, propagate: false)
                            jobReferences << new JobReference(downstreamJob: shiftJob, jobName: jobName, parameters: args, propagate: false)
                        }
                    }

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        def shift_downstream_matrix = branchfile.shift_downstream_matrix
                        LibCommonCps.triggerDownstreamJobs(this, shift_downstream_matrix, 'shift', env.branch_name, branchfile, code_changelist, data_changelist)
                    }
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchfile.standard_jobs_settings?.slack_channel_shift ?: [channels: ['#cobra-outage-shift']]
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
