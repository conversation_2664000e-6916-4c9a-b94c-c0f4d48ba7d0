# packer_windows Terraform Module

This module fetches the latest information of the [dre-cobra/packer-windows](https://gitlab.ea.com/dre-cobra/packer-windows/-/releases) releases in GitLab. It retrieves release JSON asset for a specified datacenter and outputs useful values for use in your Terraform configurations.

## Features

- Fetches the latest release asset (`release_asset.json`) for a given datacenter from [dre-cobra/packer-windows release](https://gitlab.ea.com/dre-cobra/packer-windows/-/releases).
- Outputs the datacenter, datacenter domain, packer file, and template name for use in downstream modules or resources.
- Validates required and optional input variables.

## Input Variables

| Name           | Type   | Required | Default                      | Description                                                                                       |
|----------------|--------|----------|------------------------------|---------------------------------------------------------------------------------------------------|
| `gitlab_token`   | string | yes      | n/a                          | The GitLab token.                                                             |
| `datacenter`     | string | yes      | n/a                          | The datacenter to search for. Must be one of: stockholm-powerstore, stockholm-powerstore-PS02, stockholm-powerstore-PS03, criterion, losangeles, stockholm. |
| `repository`     | string | no       | "dre-cobra/packer-windows"  | The GitLab repo in 'group/repo' or 'user/group/repo' format.                                      |
| `asset_name`     | string | no       | "release_asset.json"         | The asset name to look for. Must not be empty or only whitespace.                                 |

## Outputs

| Name              | Description                                                      |
|-------------------|------------------------------------------------------------------|
| `datacenter`        | The datacenter value returned by the packer-windows latest release. |
| `datacenter_domain` | The datacenter domain returned by the packer-windows latest release. |
| `packer_file`       | The packer file name or path returned by the packer-windows latest release. |
| `template_name`     | The template name returned by the packer-windows latest release.  |

## Example Terraform Plan

```sh
cd modules/packer_windows

terraform init

terraform plan -var="datacenter=stockholm" -var="gitlab_token=xxxx-xxxxxxxxxxxx"

  data.external.packer_windows: Reading...
  data.external.packer_windows: Read complete after 1s [id=-]

  Changes to Outputs:
    + datacenter        = "stockholm"
    + datacenter_domain = "vc.dice.ad.ea.com"
    + packer_file       = "22H2_template_base_jumper_light.pkr.hcl"
    + template_name     = "win10_22H2-cobra-light-v1.1418.fc58c351"

  You can apply this plan to save these new output values to the Terraform state, without changing any real infrastructure.
```

## Usage example in projects

```hcl
module "packer_windows" {
  source       = "../../modules/packer_windows"
  datacenter   = "stockholm"
  gitlab_token = var.gitlab_token
  # repository  = "dre-cobra/packer-windows" # Optional, override if needed
  # asset_name  = "release_asset.json"       # Optional, override if needed
}

variable "gitlab_token" {
  description = "GitLab token for API access."
  type        = string
}

# Replace usages of "var.packer_template" for "module.packer_windows.template_name"
module "dynamic_local_module_primary" {
  # ...
  vsphere_template        = try(each.value.packer_template, module.packer_windows.template_name)
  # ...
}

# Keep track of the change in the plan with
output "packer_windows_template_name" {
  value = module.packer_windows.template_name
}
```
