"""
copy_integrate_compile.py

This is a special type of integration, where we combine three steps:
- Copy some parts of a stream, according to one branch mapping.
- Integrate other parts of the same stream, according to another branch mapping.
- Compile the result to see that it's functional.
This is done before we submit the result to Perforce, if any step fails we abort the job
(and therefore avoid checking in a bad result into Perforce).

Script created as part of https://jaas.ea.com/browse/COBRA-341,
more detailed description can be found there.
"""
import os

import click
from elipy2 import (
    core,
    data,
    exceptions,
    filer,
    local_paths,
    LOGGER,
    p4,
    running_processes,
)
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.dbxmerge import DBXMerge
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.snowcache_utils import SNOWCACHE_MODES


@click.command(
    "copy_integrate_build", short_help="Performs copy+integration+compile and submits the result."
)
@click.option("--changelist", required=True)
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option("--copy-mapping", default=None)
@click.option(
    "--data-directory",
    default=None,
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--framework-args", multiple=True, help="Framework arguments for gensln.")
@click.option(
    "--ignore-source-history",
    is_flag=True,
    help="Ignore source file history (sets the Perforce integrate flag -Di).",
)
@click.option("--integrate-mapping", default=None)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--override-branch-guardian", is_flag=True, help="Override Branch Guardian when submitting."
)
@click.option("--p4-client", required=True)
@click.option("--p4-port", required=True)
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option(
    "--shelve-cl/--no-shelve-cl", default=False, help="Shelve changelist for failed integration."
)
@click.option("--source-branch", required=True, help="Source branch in Perforce.")
@click.option("--submit/--no-submit", default=True)
@click.option("--submit-message", default="", help="Message to include in the submit message.")
@click.option("--use-snowcache", is_flag=True, help="Whether to enable Snowcache or not")
@click.option(
    "--snowcache-mode-override",
    type=click.Choice(SNOWCACHE_MODES, case_sensitive=False),
    default="",
    help="Override the logically evaluated snowcache mode with this",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    changelist,
    clean,
    copy_mapping,
    data_directory,
    email,
    domain_user,
    framework_args,
    ignore_source_history,
    integrate_mapping,
    licensee,
    override_branch_guardian,
    p4_client,
    p4_port,
    p4_user,
    password,
    shelve_cl,
    source_branch,
    submit,
    submit_message,
    use_snowcache,
    snowcache_mode_override,
):
    """
    Performs a copy and integration in Perforce, compiles the result and submits it.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()
    core.clean_temp()
    core.close_file_handles(local_paths.get_packages_path())
    core.close_file_handles(local_paths.get_tnt_localpackages_path())

    # Initialize
    _filer = filer.FilerUtils()
    perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)

    copy_mapping = False if copy_mapping in [None, False, "false", "null"] else copy_mapping
    integrate_mapping = (
        False if integrate_mapping in [None, False, "false", "null"] else integrate_mapping
    )

    # Set data directory.
    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    # Revert the Perforce state
    perforce.revert(quiet=True)

    if copy_mapping:
        # Copy the engine parts of the code.
        LOGGER.info(
            "Performing copy using client %s on server %s with branch mapping %s.",
            p4_client,
            p4_port,
            copy_mapping,
        )
        perforce.copy_mapping(mapping=copy_mapping, to_revision=changelist)

    if integrate_mapping:
        # Integrate the non-engine parts of the code.
        LOGGER.info(
            "Performing integration using client %s on server %s with branch mapping %s.",
            p4_client,
            p4_port,
            integrate_mapping,
        )
        perforce.integrate(
            mapping=integrate_mapping,
            to_revision=changelist,
            ignore_source_history=ignore_source_history,
        )

        # Resolve the result from the combined copy and integration.
        resolve_mode = "m"  # Accept the result of the automatic merge
        perforce.resolve(mode=resolve_mode)

        if DBXMerge.executable:
            dbx_files = perforce.unresolved("*.dbx", resolve_type="content")
            if dbx_files:
                with DBXMerge(perforce.port, perforce.user, perforce.client) as dbxmerge:
                    for dbx_file in dbx_files:
                        dbxmerge.resolve(dbx_file.local_path)

        if perforce.unresolved():
            LOGGER.error("Unable to automatically resolve the result. Reverting and aborting!")
            if shelve_cl:
                pending_cl = perforce.latest_pending_changelist()
                if pending_cl:
                    perforce.set_description(
                        pending_cl, "Shelved changelist from failed integration."
                    )
                    perforce.shelve(pending_cl, discard=False)
            perforce.revert(quiet=True)
            raise exceptions.AutomaticP4MergeResolveException

    try:
        # Build the result to verify that we have a good result.
        compile_code(
            licensee=list(licensee),
            password=password,
            email=email,
            domain_user=domain_user,
            framework_args=list(framework_args),
            overwrite_p4config=True,
            clean=clean.lower() == "true",
            use_snowcache=use_snowcache,
            snowcache_mode_override=snowcache_mode_override,
        )

        # Submit the result to Perforce.
        message = (
            f"Copied and integrated from {source_branch}@{changelist}."
            f"{chr(10) + '#branchguardian_bypass' if override_branch_guardian else ''}"
        )
        if submit_message:
            message += f"\n{submit_message}"
        submit_integration(p4_object=perforce, submit_message=message, submit=submit)

    finally:
        perforce.revert(quiet=True)
