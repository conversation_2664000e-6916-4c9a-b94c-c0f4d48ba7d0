package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_SP_release {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call_criterion + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map code_settings = [
        fake_ooa_wrapped_symbol      : false,
        run_code_unittests           : false,
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        smoke_cl_after_success       : false,
        statebuild_code              : true, // TODO: Change to false when we have dedicated machines.
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        deployment_data_branch       : true,
        deployment_data_reference_job: 'CH1-SP-release.data.start',
        enable_daily_data_clean      : true,
        enable_lkg_cleaning          : true,
        poolbuild_data               : true, // TODO: Remove (i.e. use default false) when we have dedicated machines.
        skip_standalone_patchdata    : true,
        slack_channel_patchdata      : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        // statebuild_data              : false, // TODO: Activate when we have dedicated machines.
        timeout_hours_data           : 6,
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                              : 'CombinedShippingSPLevels',
        baseline_set                       : false,
        first_patch                        : true,
        clean_data_validation_pipeline_args: ' --disable-caches true',
        combine_bundles                    : [
            is_target_branch: false,
        ],
        enable_clean_build_validation      : false,
        enable_lkg_p4_counters             : true,
        extra_data_args                    : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        import_avalanche_autotest          : false,
        linux_docker_images                : false,
        move_location_bundles              : ['DiceStockholm'],
        move_location_bundles_types        : ['bundles', 'combine_bundles'],
        move_location_parallel             : true,
        new_locations                      : [
            DiceStockholm: [
                elipy_call_new_location: [
                    project.elipy_call,
                    '--use-fbenv-core'
                ].join(' '),

            ],
        ],
        quickscope_db                      : 'kinpipeline',
        quickscope_import                  : true,
        server_asset                       : 'Game/Setup/Build/DevMPLevels',
        single_stream_smoke                : true,
        skip_icepick_settings_file         : true,
        strip_symbols                      : false,
        timeout_hours_clean_data_validation: 20,
        timeout_hours_patchdata            : 10,
        upgrade_data_job                   : true,
        use_deprecated_blox_packages       : true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'ps5', configs: ['final', 'retail']],
        [name: 'xbsx', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.bfdata.upgrade.data', args: ['code_changelist'], trigger_only_on_new_code: true],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.patchdata.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
    ]
    static List patchdata_downstream_matrix = [
        [name: 'CH1-release.patchfrosty.start', args: []],
        [name: 'CH1-bflabs-qol.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
