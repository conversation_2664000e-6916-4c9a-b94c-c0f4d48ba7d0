@echo off
setlocal

echo ================================================
echo Elipy Shift Upload Debug Environment Setup
echo ================================================

REM Configuration
set PYTHON_EXE="C:\Program Files\Python311\python.exe"
set ELIPY_CONFIG=%~dp0pycharm\elipy2\elipy2\elipy_example.yml
set TNT_ROOT=%~dp0tnt_root
set GAME_DATA_DIR=%~dp0game_data_dir
set GAME_ROOT=%~dp0game_root
set ELIPY_TEST_RUN=TRUE

REM Add elipy modules to Python path
set PYTHONPATH=%~dp0pycharm\elipy2;%~dp0pycharm\elipy-scripts;%PYTHONPATH%

REM Create required directories
if not exist "%TNT_ROOT%" mkdir "%TNT_ROOT%"
if not exist "%GAME_DATA_DIR%" mkdir "%GAME_DATA_DIR%"
if not exist "%GAME_ROOT%" mkdir "%GAME_ROOT%"

echo Environment configured:
echo - Python: %PYTHON_EXE%
echo - ELIPY_CONFIG: %ELIPY_CONFIG%
echo - TNT_ROOT: %TNT_ROOT%
echo.

REM Verify elipy2 can be imported
echo Verifying elipy2 installation...
%PYTHON_EXE% -c "import sys; sys.path.insert(0, r'%~dp0pycharm\elipy2'); sys.path.insert(0, r'%~dp0pycharm\elipy-scripts'); import elipy2, dice_elipy_scripts; print('✓ elipy2 and dice_elipy_scripts ready')" 2>nul
if errorlevel 1 (
    echo ✗ ERROR: Failed to import elipy modules
    echo Please check that the pycharm\elipy2 and pycharm\elipy-scripts directories exist
    pause
    exit /b 1
)

echo ✓ elipy2 modules imported successfully
echo.

echo ================================================
echo Ready to run shift upload debug!
echo ================================================
echo.
echo USAGE:
echo Replace the placeholders below with your actual credentials:
echo.
echo %PYTHON_EXE% -m dice_elipy_scripts.submit_to_shift ^
echo   --user <EMAIL> ^
echo   --password YOUR_SHIFT_PASSWORD ^
echo   --code-branch CH1-content-dev ^
echo   --code-changelist 24664622 ^
echo   --data-branch CH1-content-dev ^
echo   --data-changelist 24664622 ^
echo   --artifactory-user YOUR_ARTIFACTORY_USER ^
echo   --artifactory-apikey YOUR_ARTIFACTORY_API_KEY ^
echo   --use-elipy-config ^
echo   --compression true ^
echo   --shifter-type frosty_shifter ^
echo   --force-reshift true ^
echo   --build-id None ^
echo   --debug-print-only
echo.
echo ================================================
echo Or save this as a .bat file with your credentials:
echo ================================================

REM Create a template command file
echo @echo off > run_my_shift_debug.bat
echo setlocal >> run_my_shift_debug.bat
echo. >> run_my_shift_debug.bat
echo REM Set environment >> run_my_shift_debug.bat
echo set PYTHON_EXE="C:\Program Files\Python311\python.exe" >> run_my_shift_debug.bat
echo set ELIPY_CONFIG=%~dp0pycharm\elipy2\elipy2\elipy_example.yml >> run_my_shift_debug.bat
echo set TNT_ROOT=%~dp0tnt_root >> run_my_shift_debug.bat
echo set GAME_DATA_DIR=%~dp0game_data_dir >> run_my_shift_debug.bat
echo set GAME_ROOT=%~dp0game_root >> run_my_shift_debug.bat
echo set ELIPY_TEST_RUN=TRUE >> run_my_shift_debug.bat
echo set PYTHONPATH=%~dp0pycharm\elipy2;%~dp0pycharm\elipy-scripts;%%PYTHONPATH%% >> run_my_shift_debug.bat
echo. >> run_my_shift_debug.bat
echo REM Your command - replace the placeholders with actual values >> run_my_shift_debug.bat
echo %%PYTHON_EXE%% -m dice_elipy_scripts.submit_to_shift ^^ >> run_my_shift_debug.bat
echo   --user <EMAIL> ^^ >> run_my_shift_debug.bat
echo   --password YOUR_SHIFT_PASSWORD ^^ >> run_my_shift_debug.bat
echo   --code-branch CH1-content-dev ^^ >> run_my_shift_debug.bat
echo   --code-changelist 24664622 ^^ >> run_my_shift_debug.bat
echo   --data-branch CH1-content-dev ^^ >> run_my_shift_debug.bat
echo   --data-changelist 24664622 ^^ >> run_my_shift_debug.bat
echo   --artifactory-user YOUR_ARTIFACTORY_USER ^^ >> run_my_shift_debug.bat
echo   --artifactory-apikey YOUR_ARTIFACTORY_API_KEY ^^ >> run_my_shift_debug.bat
echo   --use-elipy-config ^^ >> run_my_shift_debug.bat
echo   --compression true ^^ >> run_my_shift_debug.bat
echo   --shifter-type frosty_shifter ^^ >> run_my_shift_debug.bat
echo   --force-reshift true ^^ >> run_my_shift_debug.bat
echo   --build-id None ^^ >> run_my_shift_debug.bat
echo   --debug-print-only >> run_my_shift_debug.bat
echo. >> run_my_shift_debug.bat
echo pause >> run_my_shift_debug.bat

echo ✓ Created 'run_my_shift_debug.bat' template file
echo   Edit this file to add your actual credentials, then run it!
echo.

pause
