#!/usr/bin/env python3
"""
Test script to verify debug-print-only functionality for shift uploads
"""

import sys
import os
sys.path.insert(0, r'c:\Users\<USER>\vscode\pycharm\elipy2')
sys.path.insert(0, r'c:\Users\<USER>\vscode\pycharm\elipy-scripts')

from elipy2.shifters import FrostyShifter

def test_debug_mode():
    """Test that debug mode only prints paths without uploading"""
    
    # Create a test shifter with debug mode enabled
    shifter = FrostyShifter(
        user="test_user",
        password="test_password",
        code_branch="test_branch",
        code_changelist="12345",
        debug_print_only=True,  # Enable debug mode
        filter_latest_combined_builds=True,
        use_bilbo=False  # Disable bilbo for testing
    )
    
    print("Created FrostyShifter with debug_print_only=True")
    print("Debug flag status:", shifter.debug_print_only)
    
    # Test that the debug flag is properly set
    assert shifter.debug_print_only == True, "Debug flag should be True"
    
    print("✓ Debug mode test passed")
    
def test_normal_mode():
    """Test that normal mode has debug disabled"""
    
    # Create a test shifter with debug mode disabled (default)
    shifter = FrostyShifter(
        user="test_user", 
        password="test_password",
        code_branch="test_branch",
        code_changelist="12345",
        debug_print_only=False,  # Explicitly disable debug mode
        filter_latest_combined_builds=True,
        use_bilbo=False
    )
    
    print("Created FrostyShifter with debug_print_only=False")
    print("Debug flag status:", shifter.debug_print_only)
    
    # Test that the debug flag is properly set
    assert shifter.debug_print_only == False, "Debug flag should be False"
    
    print("✓ Normal mode test passed")

if __name__ == "__main__":
    print("Testing shift debug functionality...")
    print("=" * 50)
    
    try:
        test_debug_mode()
        test_normal_mode()
        print("=" * 50)
        print("✓ All tests passed! Debug functionality is working correctly.")
    except Exception as e:
        print(f"✗ Test failed: {e}")
        sys.exit(1)
