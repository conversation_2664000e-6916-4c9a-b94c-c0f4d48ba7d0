#!/usr/bin/env python3
"""
Simple test to verify the CLI parameter parsing for debug-print-only functionality
"""

import subprocess
import sys
import os

def test_cli_help():
    """Test that the new --debug-print-only parameter shows up in help"""
    
    try:
        # Run the CLI with --help to check if our new parameter is there
        result = subprocess.run([
            sys.executable, 
            r'c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\submit_to_shift.py',
            '--help'
        ], capture_output=True, text=True, timeout=30)
        
        help_output = result.stdout
        
        # Check if our new parameter is in the help output
        if '--debug-print-only' in help_output:
            print("✓ --debug-print-only parameter found in CLI help")
            return True
        else:
            print("✗ --debug-print-only parameter NOT found in CLI help")
            print("Help output:")
            print(help_output)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ CLI help command timed out")
        return False
    except Exception as e:
        print(f"✗ Error running CLI help: {e}")
        return False

def test_syntax_validation():
    """Test that modified files have valid Python syntax"""
    
    files_to_test = [
        r'c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\submit_to_shift.py',
        r'c:\Users\<USER>\vscode\pycharm\elipy2\elipy2\shift_utils.py',
        r'c:\Users\<USER>\vscode\pycharm\elipy2\elipy2\shifters.py'
    ]
    
    all_valid = True
    for file_path in files_to_test:
        try:
            result = subprocess.run([
                sys.executable, '-m', 'py_compile', file_path
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ {os.path.basename(file_path)} has valid syntax")
            else:
                print(f"✗ {os.path.basename(file_path)} has syntax errors:")
                print(result.stderr)
                all_valid = False
                
        except Exception as e:
            print(f"✗ Error checking syntax of {file_path}: {e}")
            all_valid = False
    
    return all_valid

if __name__ == "__main__":
    print("Testing shift debug functionality implementation...")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test syntax validation
    if test_syntax_validation():
        tests_passed += 1
    
    # Test CLI parameter
    if test_cli_help():
        tests_passed += 1
    
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Debug functionality implementation is correct.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please review the implementation.")
        sys.exit(1)
