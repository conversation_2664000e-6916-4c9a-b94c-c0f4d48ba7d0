package scripts.schedulers.all

import com.ea.lib.LibJenkins

/**
 * shift_type_scheduler.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger shifter type if needed') {
            steps {
                script {
                    def branch_name = params.branch_name ? params.branch_name.trim() : ''
                    def shifter_type = params.shifter_type ? params.shifter_type.trim() : ''
                    def downstream_job = branch_name + '.shift.' + shifter_type + '.upload'
                    def final_result = Result.FAILURE

                    // Check if previous successful build and current build are the same
                    def code_changelist = params.code_changelist ? params.code_changelist.trim() : ''
                    def combine_code_changelist = params.combine_code_changelist ? params.combine_code_changelist.trim() : ''

                    def last_good_code = LibJenkins.getLastStableCodeChangelist(downstream_job)
                    def last_good_combine_code = LibJenkins.getLastStableCombineCodeChangelist(downstream_job)

                    def was_previously_uploaded = last_good_code.toString() == code_changelist.toString()

                    // For combined builds, also check combined changelists
                    if (combine_code_changelist != '') {
                        was_previously_uploaded = was_previously_uploaded && (last_good_combine_code.toString() == combine_code_changelist.toString())
                        echo "Comparing last_good_code(${last_good_code}) with code_changelist(${code_changelist}) and last_good_combine_code(${last_good_combine_code}) with combine_code_changelist(${combine_code_changelist})"
                    } else {
                        echo "Comparing last_good_code(${last_good_code}) with code_changelist(${code_changelist})"
                    }

                    if (code_changelist) {
                        // Rename build name
                        if (combine_code_changelist != '') {
                            currentBuild.displayName = "${env.JOB_NAME}.${code_changelist}.combine.${combine_code_changelist}"
                        } else {
                            currentBuild.displayName = "${env.JOB_NAME}.${code_changelist}"
                        }

                        if (was_previously_uploaded) { // Skip re-upload
                            echo 'Code was previously uploaded to Shift. Skipping...'
                            final_result = Result.UNSTABLE
                        } else { // Upload
                            def args = [
                                string(name: 'code_changelist', value: code_changelist),
                            ]
                            // Add combined changelist if available
                            if (combine_code_changelist != '') {
                                args += [
                                    string(name: 'combine_code_changelist', value: combine_code_changelist),
                                ]
                            }
                            build(job: downstream_job, parameters: args, propagate: false)
                            final_result = Result.SUCCESS
                        }
                    } else {
                        echo 'Error: changelist parameter is null or empty'
                        final_result = Result.FAILURE
                    }

                    currentBuild.result = final_result.toString()
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
