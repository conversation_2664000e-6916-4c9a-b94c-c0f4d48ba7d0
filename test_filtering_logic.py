"""
Simplified test script to verify the combined build filtering functionality.
Tests the logic directly without importing the full elipy2 module.
"""

import os
import sys


def _filter_latest_combined_builds(shift_dirs, filter_combined_builds=True):
    """
    Filter combined builds to only keep the latest one per platform/configuration.
    
    For combined builds with path structure:
    .../platform/digital_combined/patch_combine/config/combine-data-branch/combine-data-changelist/combine-code-branch/combine-code-changelist
    
    Groups builds by platform/config and keeps only the latest (highest changelist numbers) for each group.
    
    Args:
        shift_dirs: List of shift directory paths
        filter_combined_builds: If True, filter combined builds to latest only. If False, return all builds.
        
    Returns:
        Filtered list of shift directory paths
    """
    if not filter_combined_builds:
        print("Combined build filtering disabled, keeping all builds")
        return shift_dirs
        
    combined_builds = {}
    regular_builds = []
    
    for shift_dir in shift_dirs:
        path_parts = shift_dir.split(os.sep)
        
        # Check if this is a combined build path
        if "digital_combined" in path_parts and "patch_combine" in path_parts:
            try:
                # Extract components from combined build path
                # Path structure: .../platform/digital_combined/patch_combine/config/combine-data-branch/combine-data-changelist/combine-code-branch/combine-code-changelist
                digital_combined_idx = path_parts.index("digital_combined")
                
                # Get platform (before digital_combined)
                platform = path_parts[digital_combined_idx - 1]
                
                # Get config (after patch_combine)
                patch_combine_idx = path_parts.index("patch_combine")
                config = path_parts[patch_combine_idx + 1]
                
                # Get changelist numbers for comparison
                # combine-data-changelist is after combine-data-branch
                # combine-code-changelist is after combine-code-branch
                if len(path_parts) >= patch_combine_idx + 6:
                    combine_data_changelist = int(path_parts[patch_combine_idx + 3])
                    combine_code_changelist = int(path_parts[patch_combine_idx + 5])
                    
                    # Use platform+config as the key for grouping
                    group_key = "{}_{}".format(platform, config)
                    
                    # Compare by data changelist first, then code changelist
                    priority = (combine_data_changelist, combine_code_changelist)
                    
                    if group_key not in combined_builds or priority > combined_builds[group_key][1]:
                        combined_builds[group_key] = (shift_dir, priority)
                        print("Updated latest combined build for {}: {} (data CL: {}, code CL: {})".format(
                            group_key, shift_dir, combine_data_changelist, combine_code_changelist))
                else:
                    print("Unexpected combined build path structure: {}".format(shift_dir))
                    regular_builds.append(shift_dir)
                    
            except (ValueError, IndexError) as e:
                print("Error parsing combined build path {}: {}".format(shift_dir, e))
                regular_builds.append(shift_dir)
        else:
            # Not a combined build, keep as-is
            regular_builds.append(shift_dir)
    
    # Extract the latest combined build paths
    filtered_combined_builds = [build_info[0] for build_info in combined_builds.values()]
    
    # Combine regular builds with filtered combined builds
    filtered_builds = regular_builds + filtered_combined_builds
    
    if len(filtered_builds) < len(shift_dirs):
        print("Filtered combined builds: {} -> {} (removed {} duplicate combined builds)".format(
            len(shift_dirs), len(filtered_builds), len(shift_dirs) - len(filtered_builds)))
        
        # Log which combined builds were kept
        for group_key, (build_path, priority) in combined_builds.items():
            print("Keeping latest combined build for {}: {} (priority: {})".format(
                group_key, build_path, priority))
    
    return filtered_builds


def test_filter_latest_combined_builds_mixed_builds():
    """Test filtering with mix of combined and regular builds."""
    print("\n=== Test: Mixed combined and regular builds ===")
    shift_dirs = [
        # Regular builds (should be kept as-is)
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\files\final\ww",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\files\final\ww",
        
        # Combined builds - PS5 platform, final config (different versions)
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8766",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8767",
        
        # Combined builds - WIN64 platform, final config (different versions)
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\digital_combined\patch_combine\final\data-branch\5681\code-branch\8768",
        
        # Combined builds - PS5 platform, release config (different versions)
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\release\data-branch\5678\code-branch\8765",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\release\data-branch\5679\code-branch\8766",
    ]

    print("Input builds: {} total".format(len(shift_dirs)))
    for i, build in enumerate(shift_dirs):
        print("  {}: {}".format(i+1, build))

    filtered_dirs = _filter_latest_combined_builds(shift_dirs, True)

    print("\nFiltered builds: {} total".format(len(filtered_dirs)))
    for i, build in enumerate(filtered_dirs):
        print("  {}: {}".format(i+1, build))

    # Verify results
    regular_builds = [d for d in filtered_dirs if "digital_combined" not in d]
    combined_builds = [d for d in filtered_dirs if "digital_combined" in d]
    
    print("\nVerification:")
    print("  Regular builds kept: {} (expected: 2)".format(len(regular_builds)))
    print("  Combined builds kept: {} (expected: 3)".format(len(combined_builds)))
    
    # Check specific builds
    ps5_final_builds = [d for d in combined_builds if "ps5" in d and r"\final\data-branch" in d]
    win64_final_builds = [d for d in combined_builds if "win64" in d and r"\final\data-branch" in d]
    ps5_release_builds = [d for d in combined_builds if "ps5" in d and r"\release\data-branch" in d]
    
    print("  PS5 final builds: {} (should contain 5680)".format(len(ps5_final_builds)))
    if ps5_final_builds:
        print("    {}".format(ps5_final_builds[0]))
    
    print("  WIN64 final builds: {} (should contain 5681)".format(len(win64_final_builds)))
    if win64_final_builds:
        print("    {}".format(win64_final_builds[0]))
        
    print("  PS5 release builds: {} (should contain 5679)".format(len(ps5_release_builds)))
    if ps5_release_builds:
        print("    {}".format(ps5_release_builds[0]))

    # Verify correctness
    success = (
        len(regular_builds) == 2 and
        len(combined_builds) == 3 and
        len(ps5_final_builds) == 1 and "5680" in ps5_final_builds[0] and
        len(win64_final_builds) == 1 and "5681" in win64_final_builds[0] and
        len(ps5_release_builds) == 1 and "5679" in ps5_release_builds[0]
    )
    
    print("\n✅ Test PASSED" if success else "\n❌ Test FAILED")
    return success


def test_filter_disabled():
    """Test that filtering can be disabled."""
    print("\n=== Test: Filtering disabled ===")
    shift_dirs = [
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8766",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8767",
    ]

    print("Input builds: {} total".format(len(shift_dirs)))
    filtered_dirs = _filter_latest_combined_builds(shift_dirs, False)

    # When filtering is disabled, all builds should be kept
    success = len(filtered_dirs) == 3 and set(filtered_dirs) == set(shift_dirs)
    print("Filtered builds: {} total (expected: 3)".format(len(filtered_dirs)))
    print("\n✅ Test PASSED" if success else "\n❌ Test FAILED")
    return success


def test_no_combined_builds():
    """Test filtering with only regular builds."""
    print("\n=== Test: No combined builds ===")
    shift_dirs = [
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\files\final\ww",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\files\final\ww",
        r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\linux\files\final\ww",
    ]

    print("Input builds: {} total".format(len(shift_dirs)))
    filtered_dirs = _filter_latest_combined_builds(shift_dirs, True)

    # All regular builds should be kept unchanged
    success = len(filtered_dirs) == 3 and set(filtered_dirs) == set(shift_dirs)
    print("Filtered builds: {} total (expected: 3)".format(len(filtered_dirs)))
    print("\n✅ Test PASSED" if success else "\n❌ Test FAILED")
    return success


def run_all_tests():
    """Run all tests and report results."""
    print("Testing Combined Build Filtering Logic")
    print("=" * 60)
    
    tests = [
        test_filter_latest_combined_builds_mixed_builds,
        test_filter_disabled,
        test_no_combined_builds,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print("Test Results: {}/{} tests passed".format(passed, total))
    
    if passed == total:
        print("🎉 All tests passed! Combined build filtering is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
