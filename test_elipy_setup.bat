@echo off
setlocal

REM Set Python path
set PYTHON_EXE="C:\Program Files\Python311\python.exe"
set PYTHONPATH=%~dp0pycharm\elipy2;%~dp0pycharm\elipy-scripts

REM Test elipy2 import
echo Testing elipy2 import...
%PYTHON_EXE% -c "import sys; sys.path.insert(0, r'%~dp0pycharm\elipy2'); sys.path.insert(0, r'%~dp0pycharm\elipy-scripts'); import elipy2; print('SUCCESS: elipy2 imported successfully')"

if errorlevel 1 (
    echo FAILED: Could not import elipy2
    pause
    exit /b 1
)

echo.
echo SUCCESS: Environment is ready!
echo.
echo To run your command, execute:
echo.
echo %PYTHON_EXE% -m dice_elipy_scripts.submit_to_shift --user <EMAIL> --password YOUR_PASSWORD --code-branch CH1-content-dev --code-changelist 24664622 --data-branch CH1-content-dev --data-changelist 24664622 --artifactory-user YOUR_USER --artifactory-apikey YOUR_KEY --use-elipy-config --compression true --shifter-type frosty_shifter --force-reshift true --build-id None --debug-print-only
echo.
echo (Replace YOUR_PASSWORD, YOUR_USER, and YOUR_KEY with actual values)

pause
