#!/usr/bin/env python3
"""
Example usage of the new debug mode for shift uploads

This script demonstrates how to use the --debug-print-only flag
to see what builds would be uploaded without actually uploading them.
"""

import subprocess
import sys
import os

def run_shift_debug_example():
    """
    Example of running shift upload in debug mode
    """
    
    print("Shift Upload Debug Mode Example")
    print("=" * 40)
    
    # Example command using debug mode
    cmd_debug = [
        sys.executable, "-m", "dice_elipy_scripts.submit_to_shift",
        "--user", "test_user",
        "--password", "test_password", 
        "--code-branch", "main",
        "--code-changelist", "12345",
        "--shifter-type", "frosty_shifter",
        "--debug-print-only",  # This is the new debug flag
        "--no-bilbo"  # Disable bilbo for testing
    ]
    
    # Example command for normal operation (no debug)
    cmd_normal = [
        sys.executable, "-m", "dice_elipy_scripts.submit_to_shift", 
        "--user", "test_user",
        "--password", "test_password",
        "--code-branch", "main", 
        "--code-changelist", "12345",
        "--shifter-type", "frosty_shifter",
        "--no-bilbo"
    ]
    
    print("Debug Mode Command:")
    print(" ".join(cmd_debug))
    print()
    
    print("Normal Mode Command (for comparison):")
    print(" ".join(cmd_normal))
    print()
    
    print("What happens in debug mode:")
    print("- Builds are discovered and filtered normally")
    print("- Build paths and details are printed to console")
    print("- No actual file copying or uploading occurs")
    print("- No network connections are made")
    print("- Script exits after printing build information")
    print()
    
    print("Expected debug output:")
    print("  DEBUG MODE: Only printing build paths, no actual uploads will occur")
    print("  DEBUG MODE: Build 1/3: /path/to/build1")
    print("  DEBUG MODE: Build 2/3: /path/to/build2")
    print("  DEBUG MODE: Build 3/3: /path/to/build3")
    print()
    
    print("What happens in normal mode:")
    print("- Builds are discovered and filtered normally") 
    print("- Build validation occurs")
    print("- Files are copied to temporary staging directory")
    print("- Shift template files are created")
    print("- Actual upload to Shift servers occurs")
    print("- Build metadata is registered")

def show_debug_help():
    """
    Show help for the debug feature
    """
    
    print("Debug Mode Help")
    print("=" * 20)
    print()
    print("Parameter: --debug-print-only")
    print("Type: Flag (no value needed)")
    print("Default: False (debug mode disabled)")
    print()
    print("Usage:")
    print("  --debug-print-only           Enable debug mode")
    print("  (no flag)                    Normal operation mode")
    print()
    print("Benefits:")
    print("  • Quick verification of build discovery logic")
    print("  • Safe testing without uploading")
    print("  • Debugging configuration issues")
    print("  • Understanding what builds would be processed")
    print()
    print("Use cases:")
    print("  • Testing new build configurations")
    print("  • Verifying combined build filtering works correctly")
    print("  • Checking build paths before actual upload")
    print("  • Troubleshooting missing builds")

if __name__ == "__main__":
    run_shift_debug_example()
    print()
    show_debug_help()
