package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_hotfix {
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_criterion + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        job_label_statebuild    : 'statebuild_criterion',
        poolbuild_label         : 'poolbuild_criterion',
        p4_code_creds           : 'perforce-battlefield-criterion',
        p4_data_creds           : 'perforce-battlefield-criterion',
        p4_code_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        p4_data_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]
    static Map code_settings = [
        slack_channel_code: [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled     : true,
    ]
    static Map data_settings = [
        slack_channel_data: [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        poolbuild_data          : true,
        verify_for_preflight    : true,
    ]
    static Map frosty_settings = [
        enable_eac_win64_digital      : true,
        enable_eac_win64_steam        : true,
        enable_eac_win64_combine      : true,
        enable_eac_win64_steam_combine: true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'ShippingLevels',
        server_asset              : 'Game/Setup/Build/MPLevels',
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        skip_icepick_settings_file: true,
        strip_symbols             : false,
        new_locations             : [
            Guildford   : [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal    : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        oreans_protection         : true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64server', configs: ['final']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.start.register.verifiedForPreflight', args: ['data_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
