variable "gitlab_token" {
  description = "(Required) The GitLab token. Must not be empty."
  type        = string
  sensitive   = true
  validation {
    condition     = length(trimspace(var.gitlab_token)) > 0
    error_message = "gitlab_token must not be empty."
  }
}

variable "datacenter" {
  description = "(Required) The datacenter to search for. Must be one of: stockholm-powerstore, stockholm-powerstore-PS02, stockholm-powerstore-PS03, criterion, losangeles, stockholm. (Note: if you think there are more datacenters, please rectify this list.)"
  type        = string
  validation {
    condition     = contains(["stockholm-powerstore", "stockholm-powerstore-PS02", "stockholm-powerstore-PS03", "criterion", "losangeles", "stockholm"], trimspace(var.datacenter))
    error_message = "datacenter must be one of: stockholm-powerstore, stockholm-powerstore-PS02, stockholm-powerstore-PS03, criterion, losangeles, stockholm."
  }
}

variable "repository" {
  description = "(Optional) The GitLab repo in 'group/repo' or 'user/group/repo' format. Default: dre-cobra/packer-windows."
  type        = string
  default     = "dre-cobra/packer-windows"
  validation {
    condition     = can(regex("^([A-Za-z0-9_-]+/){1,2}[A-Za-z0-9_.-]+$", trimspace(var.repository)))
    error_message = "repository must be in the format 'group/repo' or 'user/group/repo' (e.g., 'mygroup/myrepo' or 'myuser/mygroup/myrepo')."
  }
}

variable "asset_name" {
  description = "(Optional) The asset name to look for from the Releases in https://gitlab.ea.com/dre-cobra/packer-windows/-/releases. Default: release_asset.json."
  type        = string
  default     = "release_asset.json"
  validation {
    condition     = length(trimspace(var.asset_name)) > 0
    error_message = "asset_name must not be empty or only whitespace."
  }
}

data "external" "packer_windows" {
  program = [
    "${path.module}/main.sh",
    "-d", trimspace(var.datacenter),
    "-t", trimspace(var.gitlab_token),
    "-r", trimspace(var.repository),
    "-a", trimspace(var.asset_name)
  ]
}

output "datacenter" {
  description = "The datacenter value returned by the packer-windows latest release."
  value       = trimspace(data.external.packer_windows.result["datacenter"])
}

output "packer_file" {
  description = "The packer file name or path returned by the packer-windows latest release."
  value       = trimspace(data.external.packer_windows.result["packer_file"])
}

output "datacenter_domain" {
  description = "The datacenter domain returned by the packer-windows latest release."
  value       = trimspace(data.external.packer_windows.result["datacenter_domain"])
}

output "template_name" {
  description = "The template name returned by the packer-windows latest release."
  value       = trimspace(data.external.packer_windows.result["template_name"])
}
