#**********************************************
#               kin-test-ps PIPE
#**********************************************
.default-kin-test-ps-variables:
  extends: .secrets-kin-test-ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kin-test-ps"
    WORKING_DIR: "projects/kin-test-ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kin-test.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps.json"
    ANSIBLE_BRANCH: COBRA-5283 # Testing upgrade of winsw
    #Adding docker controller here to use latest to test anisble upgrade.

prepare-json-config-kin-test-ps:
  extends: ['.default-kin-test-ps-variables', '.prepare_config']
  tags:
    - glaas-shared-k8s

validate-kin-test-ps:
  extends: ['.default-kin-test-ps-variables', '.validation_steps']
  tags:
    - glaas-shared-k8s

plan-kin-test-ps:
  needs:
    - job: validate-kin-test-ps
    - job: prepare-json-config-kin-test-ps
  extends: ['.default-kin-test-ps-variables','.plan_steps']
  tags:
    - glaas-shared-k8s

apply-kin-test-ps:
  needs:
    - job: plan-kin-test-ps
    - job: prepare-json-config-kin-test-ps
  extends: ['.default-kin-test-ps-variables','.apply_steps']
  tags:
    - glaas-shared-k8s

attache-kin-test-ps:
  needs:
    - job: apply-kin-test-ps
    - job: prepare-json-config-kin-test-ps
  extends: ['.default-kin-test-ps-variables','.attache_vmdk_step']
  tags:
    - glaas-shared-k8s

sync-kin-test-ps:
  needs:
    - job: apply-kin-test-ps
    - job: attache-kin-test-ps
    - job: prepare-json-config-kin-test-ps
  extends: ['.default-kin-test-ps-variables','.sync_vmdk_step']

ansible-kin-test-ps:
  needs:
    - job: apply-kin-test-ps
    - job: sync-kin-test-ps
    - job: prepare-json-config-kin-test-ps
  extends: ['.default-kin-test-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
  tags:
    - glaas-shared-k8s
