@echo off 
setlocal 
 
REM Set environment 
set PYTHON_EXE="C:\Program Files\Python311\python.exe" 
set ELIPY_CONFIG=C:\Users\<USER>\vscode\pycharm\elipy2\elipy2\elipy_example.yml 
set TNT_ROOT=C:\Users\<USER>\vscode\tnt_root 
set GAME_DATA_DIR=C:\Users\<USER>\vscode\game_data_dir 
set GAME_ROOT=C:\Users\<USER>\vscode\game_root 
set ELIPY_TEST_RUN=TRUE 
set PYTHONPATH=C:\Users\<USER>\vscode\pycharm\elipy2;C:\Users\<USER>\vscode\pycharm\elipy-scripts;%PYTHONPATH% 
 
REM Your command - replace the placeholders with actual values 
%PYTHON_EXE% -m dice_elipy_scripts.submit_to_shift ^ 
  --user <EMAIL> ^ 
  --password YOUR_SHIFT_PASSWORD ^ 
  --code-branch CH1-content-dev ^ 
  --code-changelist 24664622 ^ 
  --data-branch CH1-content-dev ^ 
  --data-changelist 24664622 ^ 
  --artifactory-user YOUR_ARTIFACTORY_USER ^ 
  --artifactory-apikey YOUR_ARTIFACTORY_API_KEY ^ 
  --use-elipy-config ^ 
  --compression true ^ 
  --shifter-type frosty_shifter ^ 
  --force-reshift true ^ 
  --build-id None ^ 
  --debug-print-only 
 

