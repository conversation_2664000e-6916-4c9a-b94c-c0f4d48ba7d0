#!/usr/bin/env python3
"""
Test script to debug the shift build filtering issue
"""

import os

def analyze_build_paths():
    """Analyze the build paths from the user's example to understand the issue"""
    
    build_paths = [
        r"ps5\digital_combine\ww\final\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497",
        r"linux64\files\ww\final",
        r"win64\steam_combine\ww\final\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497",
        r"win64\digital_combine\ww\final\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497",
        r"server\files\ww\final",
        r"linuxserver\files\ww\final",
        r"server\digital\ww\final",
        r"linuxserver\digital\ww\final",
        r"win64\steam_combine\ww\retail\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497",
        r"win64\digital_combine\ww\retail\ch1-sp-content-dev\24702497\ch1-sp-content-dev\24702497",
        r"ps5\files\dev\performance",
        r"ps5\files\dev\final",
        r"xbsx\files\ww\final",
        r"win64\files\ww\release",
        r"xbsx\files\ww\performance",
        r"win64\files\ww\final",
        r"win64\files\ww\performance"
    ]
    
    print("Build Path Analysis")
    print("=" * 50)
    print(f"Total builds in example: {len(build_paths)}")
    print()
    
    combined_builds = {}
    regular_builds = []
    
    for build_path in build_paths:
        path_parts = build_path.split(os.sep)
        
        # Check for combined build types
        combined_types = ["digital_combine", "steam_combine", "patch_combine", "digital_combined"]
        found_combined_type = None
        for combine_type in combined_types:
            if combine_type in path_parts:
                found_combined_type = combine_type
                break
        
        if found_combined_type:
            print(f"Combined build ({found_combined_type}): {build_path}")
            
            try:
                combined_idx = path_parts.index(found_combined_type)
                platform = path_parts[combined_idx - 1]
                config = path_parts[combined_idx + 1]
                
                # Look for numeric changelist values
                data_changelist_found = False
                code_changelist_found = False
                combine_data_changelist = 0
                combine_code_changelist = 0
                
                for i in range(combined_idx + 1, len(path_parts)):
                    try:
                        changelist_num = int(path_parts[i])
                        if not data_changelist_found:
                            combine_data_changelist = changelist_num
                            data_changelist_found = True
                        elif not code_changelist_found:
                            combine_code_changelist = changelist_num
                            code_changelist_found = True
                            break
                    except ValueError:
                        continue
                
                if data_changelist_found and code_changelist_found:
                    group_key = f"{platform}_{config}_{found_combined_type}"
                    priority = (combine_data_changelist, combine_code_changelist)
                    
                    if group_key not in combined_builds or priority > combined_builds[group_key][1]:
                        if group_key in combined_builds:
                            print(f"  Replacing {group_key}: {combined_builds[group_key][0]} (priority {combined_builds[group_key][1]})")
                            print(f"  With: {build_path} (priority {priority})")
                        else:
                            print(f"  New group {group_key}: {build_path} (priority {priority})")
                        combined_builds[group_key] = (build_path, priority)
                    else:
                        print(f"  Skipping {group_key}: {build_path} (priority {priority}) - lower than {combined_builds[group_key][1]}")
                else:
                    print(f"  Warning: Could not find both changelists in {build_path}")
                    regular_builds.append(build_path)
            except (ValueError, IndexError) as e:
                print(f"  Error parsing {build_path}: {e}")
                regular_builds.append(build_path)
        else:
            print(f"Regular build: {build_path}")
            regular_builds.append(build_path)
    
    print()
    print("Filtering Results:")
    print("=" * 30)
    print(f"Regular builds: {len(regular_builds)}")
    print(f"Combined build groups: {len(combined_builds)}")
    print(f"Total after filtering: {len(regular_builds) + len(combined_builds)}")
    print(f"Builds removed: {len(build_paths) - (len(regular_builds) + len(combined_builds))}")
    
    print()
    print("Final build list after filtering:")
    for build in regular_builds:
        print(f"  KEEP (regular): {build}")
    
    for group_key, (build_path, priority) in combined_builds.items():
        print(f"  KEEP (combined {group_key}): {build_path}")

if __name__ == "__main__":
    analyze_build_paths()
