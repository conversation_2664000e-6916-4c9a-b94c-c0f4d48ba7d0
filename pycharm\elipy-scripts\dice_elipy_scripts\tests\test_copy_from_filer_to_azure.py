"""
test_copy_from_filer_to_azure.py

Unit testing for  copy_from_filer_to_azure
"""
import tempfile
import mock
import pytest
import os
from unittest.mock import MagicMock, patch

import dice_elipy_scripts.copy_from_filer_to_azure
from dice_elipy_scripts.copy_from_filer_to_azure import (
    cli,
    verify_source,
    verify_destination,
    verify_content_type,
    get_code_src_and_dest,
    register_build_in_bilbo,
    list_files_in_directory,
)
from click.testing import CliRunner


class TestCopyFromFilerToAzure:
    @pytest.mark.parametrize(
        "content_type,should_raise_exception",
        [("code", False), ("unsupported_content_type", True)],
    )
    def test_verify_content_type(self, content_type, should_raise_exception):
        if not should_raise_exception:
            verify_content_type(content_type)
        else:
            with pytest.raises(Exception) as e:
                verify_content_type(content_type)

    @pytest.mark.parametrize(
        "path,exists,should_raise_exception",
        [
            ("/path/to/existing/file", True, False),  # expected to pass
            ("/path/to/non_existing/file", False, True),  # expected to fail
        ],
    )
    @patch("os.path.exists")
    def test_verify_source(self, mock_exists, path, exists, should_raise_exception):
        mock_exists.return_value = exists
        if not should_raise_exception:
            verify_source(path)
        else:
            with pytest.raises(Exception) as e:
                verify_source(path)

    @pytest.mark.parametrize(
        "path,exists,force_overwrite,should_raise_exception",
        [
            ("/path/to/existing/file", True, False, True),
            ("/path/to/non_existing/file", False, False, False),
            ("/path/to/non_existing/file", True, True, False),
        ],
    )
    @patch("os.path.exists")
    def test_verify_destination(
        self, mock_exists, path, exists, force_overwrite, should_raise_exception
    ):
        mock_exists.return_value = exists
        if not should_raise_exception:
            verify_destination(path, force_overwrite=force_overwrite)
        else:
            with pytest.raises(Exception) as e:
                verify_destination(path, force_overwrite=force_overwrite)

    @patch("dice_elipy_scripts.copy_from_filer_to_azure.verify_content_type", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.verify_source", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.verify_destination", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer.FilerUtils", MagicMock())
    @patch(
        "dice_elipy_scripts.copy_from_filer_to_azure.get_code_src_and_dest",
        MagicMock(return_value=("source", "destination")),
    )
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.authenticate_filer", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.set_licensee", MagicMock())
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer.FilerUtils")
    def test_cli_runs_successfully_with_appropriate_args(self, mock_filer_utils):
        platform = dice_elipy_scripts.copy_from_filer_to_azure.SUPPORTED_PLATFORMS[0]
        content_type = dice_elipy_scripts.copy_from_filer_to_azure.SUPPORTED_CONTENT_TYPES[0]
        mock_filer = mock_filer_utils.return_value = MagicMock()
        test_args = [
            "--content-type",
            content_type,
            "--source",
            "test_source",
            "--destination",
            "test_destination",
            "--platform",
            platform,
            "--config",
            "release",
            "--code-branch",
            "test_code_branch",
            "--code-changelist",
            "12345",
            "--elipy-config-location",
            "test_elipy_config_location",
            "--target-build-share",
            "test_target_build_share",
            "--secret-context",
            "test_secret_context",
            "--licensee",
            "test_licensee",
        ]

        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0

    @pytest.mark.parametrize(
        "source,destination",
        [
            (
                "\\\\filer.dice.ad.ea.com\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release\\",
                "\\\\***********\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release\\",
            ),
            (
                "\\\\filer.dice.ad.ea.com\\Builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release",
                "\\\\***********\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release",
            ),
            (
                "\\\\filer.dice.ad.ea.com\\Builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release",
                "\\\\***********\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release",
            ),
            (
                "D:\\dev\\TnT\\Local\\Bin\\Win64-dll",
                "\\\\***********\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release",
            ),
        ],
    )
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer_paths")
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer.FilerUtils")
    def test_get_code_src_and_dest_builds_correct_destination_path(
        self,
        mock_filer_utils,
        mock_filer_paths,
        source,
        destination,
    ):
        mock_filer = mock_filer_utils.return_value = MagicMock()
        mock_filer.fetch_code.return_value = MagicMock()
        mock_filer_paths.get_code_build_platform_path.return_value = destination

        src_result, dest_result = get_code_src_and_dest(
            mock_filer,
            source,
            None,
            "code_branch",
            "12345",
            platform="tool",
            additional_tools_to_include=["frostedtests"],
            config="config",
            elipy_config_location="push_to_azure_tests",
            target_build_share="target_build_share",
        )
        assert src_result == source
        assert dest_result == destination

    @patch("elipy2.filer.core.use_bilbo", return_value=True)
    @patch("elipy2.build_metadata_utils.setup_metadata_manager")
    def test_register_build_in_bilbo(self, mock_setup_metadata_manager, mock_use_bilbo):
        mock_bilbo_instance = mock_setup_metadata_manager.return_value
        source = "\\\\filer.dice.ad.ea.com\\builds\\battlefield\\code\\trunk-code-dev\\19239137\\win64-dll\\release\\"

        register_build_in_bilbo(source)
        mock_use_bilbo.assert_called_once()
        mock_bilbo_instance.register_azure_build.assert_called_once_with(source, mock.ANY)

    @patch("dice_elipy_scripts.copy_from_filer_to_azure.local_paths")
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer_paths")
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer.FilerUtils")
    def test_get_code_src_and_dest_no_source_and_destination(
        self,
        mock_filer_utils,
        mock_filer_paths,
        mock_local_paths,
    ):
        mock_filer = mock_filer_utils.return_value = MagicMock()
        mock_filer.fetch_code.return_value = MagicMock()

        mock_local_paths.get_local_build_path.return_value = "test_source"
        mock_filer_paths.get_code_build_platform_path.return_value = "test_destination"

        src_result, dest_result = get_code_src_and_dest(
            mock_filer,
            None,
            None,
            "code_branch",
            "12345",
            "platform",
            ["frostedtests"],
            "config",
            "push_to_azure_tests",
            "target_build_share",
        )
        assert src_result == "test_source"
        assert dest_result == os.path.join("test_destination", "config")

    @pytest.mark.parametrize(
        "target_platform,additional_tools,expected_platforms",
        [
            ("tool", None, ["tool", "pipeline", "frosted"]),
            ("tool", ["frostedtests"], ["tool", "pipeline", "frosted", "frostedtests"]),
            (
                "tool",
                ["frostedtests", "anothertool"],
                ["tool", "pipeline", "frosted", "frostedtests", "anothertool"],
            ),
            ("ps5", None, ["ps5"]),
            ("ps5", ["frostedtests"], ["ps5"]),  # additonals are ignored for non-tool platforms
        ],
    )
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.local_paths")
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer_paths")
    @patch("dice_elipy_scripts.copy_from_filer_to_azure.filer.FilerUtils")
    def test_get_code_src_and_dest_fetches_correct_additional_tools(
        self,
        mock_filer_utils,
        mock_filer_paths,
        mock_local_paths,
        target_platform,
        additional_tools,
        expected_platforms,
    ):
        mock_filer = mock_filer_utils.return_value = MagicMock()

        x = mock_filer.fetch_code = MagicMock()
        mock_filer.fetch_code.return_value = MagicMock()

        mock_local_paths.get_local_build_path.return_value = "test_source"
        mock_filer_paths.get_code_build_platform_path.return_value = "test_destination"

        _, _ = get_code_src_and_dest(
            mock_filer,
            None,
            None,
            "code_branch",
            "12345",
            target_platform,
            additional_tools,
            "config",
            "push_to_azure_tests",
            "target_build_share",
        )
        assert mock_filer.fetch_code.call_count == len(expected_platforms)
        num_called_platforms = 0
        for call_args in mock_filer.fetch_code.call_args_list:
            called_platform = call_args[0][2]
            assert called_platform in expected_platforms
            num_called_platforms += 1
        assert len(expected_platforms) == num_called_platforms

    @patch("dice_elipy_scripts.copy_from_filer_to_azure.LOGGER")
    def test_list_files_in_directory_with_files_and_dirs(self, mock_logger):
        temp_dir = tempfile.mkdtemp()
        test_file1 = os.path.join(temp_dir, "file1.txt")
        test_file2 = os.path.join(temp_dir, "file2.exe")
        test_dir1 = os.path.join(temp_dir, "subdir1")
        test_dir2 = os.path.join(temp_dir, "subdir2")
        with open(test_file1, "w") as f:
            f.write("test content")
        with open(test_file2, "w") as f:
            f.write("test content")
        os.makedirs(test_dir1)
        os.makedirs(test_dir2)

        list_files_in_directory(temp_dir)

        mock_logger.info.assert_any_call(f"Listing files in: {temp_dir}")
        mock_logger.info.assert_any_call(f"File: {test_file1}")
        mock_logger.info.assert_any_call(f"File: {test_file2}")
        mock_logger.info.assert_any_call(f"Directory: {test_dir1}")
        mock_logger.info.assert_any_call(f"Directory: {test_dir2}")
        mock_logger.info.assert_any_call("Total: 2 files, 2 directories")
        mock_logger.warning.assert_not_called()

    @patch("dice_elipy_scripts.copy_from_filer_to_azure.LOGGER")
    def test_list_files_in_empty_directory(self, mock_logger):
        temp_dir = tempfile.mkdtemp()
        list_files_in_directory(temp_dir)

        mock_logger.info.assert_any_call(f"Listing files in: {temp_dir}")
        mock_logger.info.assert_any_call("Total: 0 files, 0 directories")
        mock_logger.warning.assert_not_called()

    @patch("dice_elipy_scripts.copy_from_filer_to_azure.LOGGER")
    def test_list_files_in_nonexistent_directory(self, mock_logger):
        temp_dir = tempfile.mkdtemp()
        nonexistent_dir = os.path.join(temp_dir, "nonexistent")

        list_files_in_directory(nonexistent_dir)

        mock_logger.info.assert_any_call(f"Listing files in: {nonexistent_dir}")
        mock_logger.info.assert_any_call(f"Directory {nonexistent_dir} does not exist yet")
        mock_logger.warning.assert_not_called()
