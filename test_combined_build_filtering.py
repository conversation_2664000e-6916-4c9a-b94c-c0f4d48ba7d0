"""
Test script to verify the combined build filtering functionality.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch

# Add the elipy2 module to the path
sys.path.insert(0, r'c:\Users\<USER>\vscode\pycharm\elipy2')

from elipy2.shift_utils import ShiftUtils


class TestCombinedBuildFiltering(unittest.TestCase):
    """Test cases for the combined build filtering functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.shift_utils = ShiftUtils(
            user="test_user",
            password="test_password",
            filter_latest_combined_builds=True,
            required_arguments=[]
        )

    def test_filter_latest_combined_builds_mixed_builds(self):
        """Test filtering with mix of combined and regular builds."""
        shift_dirs = [
            # Regular builds (should be kept as-is)
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\files\final\ww",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\files\final\ww",
            
            # Combined builds - PS5 platform, final config (different versions)
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8766",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8767",
            
            # Combined builds - WIN64 platform, final config (different versions)
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\digital_combined\patch_combine\final\data-branch\5681\code-branch\8768",
            
            # Combined builds - PS5 platform, release config (different versions)
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\release\data-branch\5678\code-branch\8765",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\release\data-branch\5679\code-branch\8766",
        ]

        filtered_dirs = self.shift_utils._filter_latest_combined_builds(shift_dirs, True)

        # Should keep all regular builds
        regular_builds = [d for d in filtered_dirs if "digital_combined" not in d]
        self.assertEqual(len(regular_builds), 2)

        # Should keep only latest combined build for each platform/config combination
        combined_builds = [d for d in filtered_dirs if "digital_combined" in d]
        self.assertEqual(len(combined_builds), 4)  # ps5_final, win64_final, ps5_release (latest of each)

        # Verify the latest builds are kept
        ps5_final_builds = [d for d in combined_builds if "ps5" in d and "final" in d]
        self.assertEqual(len(ps5_final_builds), 1)
        self.assertIn("5680", ps5_final_builds[0])  # Latest data changelist
        self.assertIn("8767", ps5_final_builds[0])  # Latest code changelist

        win64_final_builds = [d for d in combined_builds if "win64" in d and "final" in d]
        self.assertEqual(len(win64_final_builds), 1)
        self.assertIn("5681", win64_final_builds[0])  # Latest data changelist
        self.assertIn("8768", win64_final_builds[0])  # Latest code changelist

        ps5_release_builds = [d for d in combined_builds if "ps5" in d and "release" in d]
        self.assertEqual(len(ps5_release_builds), 1)
        self.assertIn("5679", ps5_release_builds[0])  # Latest data changelist
        self.assertIn("8766", ps5_release_builds[0])  # Latest code changelist

    def test_filter_latest_combined_builds_disabled(self):
        """Test that filtering can be disabled."""
        shift_dirs = [
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8766",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8767",
        ]

        filtered_dirs = self.shift_utils._filter_latest_combined_builds(shift_dirs, False)

        # When filtering is disabled, all builds should be kept
        self.assertEqual(len(filtered_dirs), 3)
        self.assertEqual(set(filtered_dirs), set(shift_dirs))

    def test_filter_latest_combined_builds_no_combined_builds(self):
        """Test filtering with only regular builds."""
        shift_dirs = [
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\files\final\ww",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\win64\files\final\ww",
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\linux\files\final\ww",
        ]

        filtered_dirs = self.shift_utils._filter_latest_combined_builds(shift_dirs, True)

        # All regular builds should be kept unchanged
        self.assertEqual(len(filtered_dirs), 3)
        self.assertEqual(set(filtered_dirs), set(shift_dirs))

    def test_filter_latest_combined_builds_invalid_path(self):
        """Test filtering with invalid combined build paths."""
        shift_dirs = [
            # Valid combined build
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5678\code-branch\8765",
            # Invalid combined build (missing parts)
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final",
            # Another valid combined build
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8766",
        ]

        filtered_dirs = self.shift_utils._filter_latest_combined_builds(shift_dirs, True)

        # Should keep the latest valid combined build and the invalid one as regular build
        self.assertEqual(len(filtered_dirs), 2)
        
        # The latest valid combined build should be kept
        valid_combined = [d for d in filtered_dirs if len(d.split(os.sep)) > 15]
        self.assertEqual(len(valid_combined), 1)
        self.assertIn("5679", valid_combined[0])  # Latest changelist

    def test_changelist_comparison_priority(self):
        """Test that data changelist takes priority over code changelist in comparison."""
        shift_dirs = [
            # Higher data changelist, lower code changelist
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5680\code-branch\8765",
            # Lower data changelist, higher code changelist
            r"\\filer\builds\frosty\project\data-branch\123\code-branch\456\ps5\digital_combined\patch_combine\final\data-branch\5679\code-branch\8767",
        ]

        filtered_dirs = self.shift_utils._filter_latest_combined_builds(shift_dirs, True)

        # Should keep the one with higher data changelist (5680)
        self.assertEqual(len(filtered_dirs), 1)
        self.assertIn("5680", filtered_dirs[0])
        self.assertIn("8765", filtered_dirs[0])


def run_tests():
    """Run all the tests and report results."""
    print("Testing combined build filtering functionality...")
    print("=" * 60)
    
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCombinedBuildFiltering)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All tests passed! Combined build filtering is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
