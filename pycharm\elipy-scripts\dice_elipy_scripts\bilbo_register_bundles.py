"""
bilbo_register_bundles.py
"""

import os

import click
from elipy2 import LOGGER, SETTINGS, bilbo
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command(
    "bilbo_register_bundles",
    short_help="""
    Registers bundles in Bilbo.
    """,
)
@click.option(
    "--code-branch",
    help="Branch/stream name for code",
    required=True,
)
@click.option(
    "--code-changelist",
    help="Changelist number for code",
    required=True,
)
@click.option(
    "--data-branch",
    help="Branch/stream name for data",
    required=True,
)
@click.option(
    "--data-changelist",
    help="Changelist number for data",
    required=True,
)
@click.option(
    "--platform",
    help="Platform for the bundles",
    required=True,
)
@click.option(
    "--dataset",
    required=True,
    help="Which dataset has been validated",
)
@click.option(
    "--bundle-type",
    help="Type of bundles to register",
    default="bundles",
    required=False,
)
@click.option(
    "--extra-location",
    multiple=True,
    required=False,
    help="Another location to register this build",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(  # pylint: disable=too-many-positional-arguments,too-many-arguments
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    platform,
    dataset,
    bundle_type,
    extra_location,
):
    """
    Registers bundles bilbo

    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    locations = list(extra_location)
    locations.insert(0, SETTINGS.location)

    for location in locations:
        bilbo_url = SETTINGS.get("bilbo_url", location)
        bilbo_utils = bilbo.BilboUtils(bilbo_url)

        LOGGER.info(
            "Registering bundles for %s %s, %s %s, platform %s, dataset %s, bundle-type %s at %s",
            code_branch,
            code_changelist,
            data_branch,
            data_changelist,
            platform,
            dataset,
            bundle_type,
            bilbo_url,
        )

        bilbo_utils.register_bundles(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_type=bundle_type,
        )

        LOGGER.info("Successfully registered bundles in Bilbo")


if __name__ == "__main__":
    cli()  # pylint: disable=no-value-for-parameter
