@echo off
setlocal

echo ================================================
echo Setting up and running elipy shift upload debug
echo ================================================

REM Set environment variables
set PYTHON_EXE="C:\Program Files\Python311\python.exe"
set ELIPY_CONFIG=%~dp0pycharm\elipy2\elipy2\elipy_example.yml
set TNT_ROOT=%~dp0tnt_root
set GAME_DATA_DIR=%~dp0game_data_dir  
set GAME_ROOT=%~dp0game_root
set ELIPY_TEST_RUN=TRUE

echo Environment variables set:
echo PYTHON_EXE=%PYTHON_EXE%
echo ELIPY_CONFIG=%ELIPY_CONFIG%
echo TNT_ROOT=%TNT_ROOT%

REM Create required directories
if not exist "%TNT_ROOT%" mkdir "%TNT_ROOT%"
if not exist "%GAME_DATA_DIR%" mkdir "%GAME_DATA_DIR%"
if not exist "%GAME_ROOT%" mkdir "%GAME_ROOT%"

REM Add the pycharm directories to Python path
set PYTHONPATH=%~dp0pycharm\elipy2;%~dp0pycharm\elipy-scripts;%PYTHONPATH%

echo ================================================
echo Testing elipy installation...
echo ================================================
%PYTHON_EXE% -c "import sys; sys.path.insert(0, r'%~dp0pycharm\elipy2'); sys.path.insert(0, r'%~dp0pycharm\elipy-scripts'); import elipy2; print('elipy2 version:', elipy2.__version__)"
if errorlevel 1 (
    echo ERROR: Failed to import elipy2
    pause
    exit /b 1
)

echo ================================================
echo Running your shift upload debug command...
echo ================================================
echo.
echo Command: %PYTHON_EXE% -m dice_elipy_scripts.submit_to_shift --user <EMAIL> --password **** --code-branch CH1-content-dev --code-changelist 24664622 --data-branch CH1-content-dev --data-changelist 24664622 --artifactory-user **** --artifactory-apikey **** --use-elipy-config --compression true --shifter-type frosty_shifter --force-reshift true --build-id None --debug-print-only
echo.

REM Run the actual command (replace **** with your actual credentials)
%PYTHON_EXE% -m dice_elipy_scripts.submit_to_shift --user <EMAIL> --password "YOUR_PASSWORD_HERE" --code-branch CH1-content-dev --code-changelist 24664622 --data-branch CH1-content-dev --data-changelist 24664622 --artifactory-user "YOUR_ARTIFACTORY_USER" --artifactory-apikey "YOUR_ARTIFACTORY_KEY" --use-elipy-config --compression true --shifter-type frosty_shifter --force-reshift true --build-id None --debug-print-only

echo ================================================
echo Command completed!
echo ================================================
pause
